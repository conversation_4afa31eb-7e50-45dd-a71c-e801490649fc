from dataclasses import dataclass
from typing import Dict, FrozenSet, Optional

from shapely.geometry import Polygon

from logistiscore.ir.network import Segment
from logistiscore.ir.slope import SlopeSegmentMeasurement
from logistiscore.ir.speed import AveragedSpeedData


@dataclass(frozen=True)
class GeographicBounds:
    min_latitude: float
    max_latitude: float
    min_longitude: float
    max_longitude: float


@dataclass(frozen=True)
class Territory:
    name: str
    geometry: Polygon

    @property
    def bounds(self) -> GeographicBounds:
        minx, miny, maxx, maxy = self.geometry.bounds
        return GeographicBounds(
            min_latitude=miny, max_latitude=maxy, min_longitude=minx, max_longitude=maxx
        )


@dataclass(frozen=True)
class TerritoryData:
    territory: Territory
    segments: Dict[int, Segment]
    speed_data: AveragedSpeedData
    slope_data: FrozenSet[SlopeSegmentMeasurement]
    segment_weights: Optional[Dict[int, float]] = None
