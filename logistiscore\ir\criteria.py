from dataclasses import dataclass
from enum import Enum


class CriteriaType(str, Enum):
    C5 = "C5"  # Vitesse
    C11 = "C11"  # Pentes

    def get_criteria(self) -> str:
        return self.value

    @staticmethod
    def from_string(criteria: str) -> "CriteriaType":
        return {
            "C5": CriteriaType.C5,
            "C11": CriteriaType.C11,
        }[criteria]


@dataclass(frozen=True)
class Threshold:
    good: float  # Borne bonne
    bad: float  # Borne mauvaise
