from typing import Dict, List

import geopandas as gpd
import pandas as pd

from logistiscore.ir.network import Segment
from logistiscore.repositories.abstract_repositories import StreetNetworkRepository


class HEREStreetNetworkRepository(StreetNetworkRepository):
    def __init__(self):
        self.required_raw_here_columns = ["LINK_ID", "ST_NAME"]

    def load_network(
        self, file_path: str, expand_directions: bool = False
    ) -> Dict[int, Segment]:
        gdf = self._read_navstreet_data(file_path)
        gdf = self._preprocess(gdf, expand_directions)

        return self._create_segments(gdf)

    def _read_navstreet_data(self, file_path: str) -> gpd.GeoDataFrame:
        try:
            gdf = gpd.read_file(file_path, columns=self.required_raw_here_columns)
            if gdf.crs is None:
                gdf.set_crs(epsg=4326, inplace=True)

            self._validate_columns(gdf, self.required_raw_here_columns)
            return gdf
        except Exception as e:
            raise ValueError(f"Error loading HERE NavStreet network: {e}")

    def _preprocess(
        self, gdf: gpd.GeoDataFrame, expand_directions: bool
    ) -> gpd.GeoDataFrame:
        if expand_directions:
            gdf = expand_street_directions(gdf)

        return self._transform_to_internal_schema(gdf, expand_directions)

    def _validate_columns(
        self, gdf: gpd.GeoDataFrame, required_columns: List[str]
    ) -> None:
        missing_columns = [col for col in required_columns if col not in gdf.columns]
        if missing_columns:
            raise ValueError(f'Missing required columns: {", ".join(missing_columns)}')

    def _transform_to_internal_schema(
        self, gdf: gpd.GeoDataFrame, expand_directions: bool
    ) -> gpd.GeoDataFrame:
        internal_data = {
            "segment_id": gdf["LINK_ID"].astype(int),
            "name": gdf["ST_NAME"],
            "geometry": gdf.geometry,
            "length": self._compute_segments_length(gdf.geometry),
        }
        if expand_directions:
            internal_data["direction"] = gdf["direction"]

        return gpd.GeoDataFrame(internal_data)

    def _create_segments(self, gdf: gpd.GeoDataFrame) -> Dict[int, Segment]:
        segments = {}
        has_direction = "direction" in gdf.columns

        for _, row in gdf.iterrows():
            segment = self._create_segment_from_row(row, has_direction)
            segments[segment.segment_id] = segment

        return segments

    def _create_segment_from_row(self, row: pd.Series, has_direction: bool) -> Segment:
        segment_kwargs = {
            "segment_id": row["segment_id"],
            "name": row["name"],
            "geometry": row["geometry"],
            "length": row["length"],
        }

        if has_direction:
            segment_kwargs["direction"] = row["direction"]

        return Segment(**segment_kwargs)

    def _compute_segments_length(self, geometry_series: gpd.GeoSeries) -> pd.Series:
        """project on the Swiss coordinate system(EPSG:21781)."""
        projected_geometries = geometry_series.copy()
        projected_geometries = projected_geometries.to_crs("EPSG:21781")
        lengths = projected_geometries.length
        lengths[lengths <= 1] = 1
        return lengths.round(3)


def expand_street_directions(gdf_street: gpd.GeoDataFrame) -> gpd.GeoDataFrame:
    """
    Pré-traite les données géospatialisées du réseau routier.

    Cette fonction duplique les tronçons routiers du GeoDataFrame d'entrée
    et applique un décalage selon le sens de circulation.

    Parameters:
    -----------
    gdf_street : gpd.GeoDataFrame
        Le GeoDataFrame d'origine contenant les tronçons routiers.

    Returns:
    --------
    gpd.GeoDataFrame
        Un nouveau GeoDataFrame avec les tronçons dirigés et décalés.
    """
    gdf_street_f = gdf_street[gdf_street["DIR_TRAVEL"].apply(lambda x: x in ["F", "B"])]
    gdf_street_t = gdf_street[gdf_street["DIR_TRAVEL"].apply(lambda x: x in ["T", "B"])]
    gdf_street_f["LINK_DIR"] = gdf_street_f["LINK_ID"].astype(str) + "F"
    gdf_street_t["LINK_DIR"] = gdf_street_t["LINK_ID"].astype(str) + "T"
    gdf_street_t["geometry"] = gdf_street_t[
        "geometry"
    ].reverse()  # Inversion de la géométrie pour les tronçons T
    # Concatenate and ensure the result is a GeoDataFrame
    combined_df = pd.concat([gdf_street_f, gdf_street_t])
    gdf_street_dir = gpd.GeoDataFrame(combined_df, geometry="geometry")
    gdf_street_dir["geometry"] = gdf_street_dir["geometry"].apply(
        lambda x: x.parallel_offset(0.00005, "right")
    )
    gdf_street_dir["direction"] = gdf_street_dir["LINK_DIR"].str[-1]
    return gdf_street_dir
