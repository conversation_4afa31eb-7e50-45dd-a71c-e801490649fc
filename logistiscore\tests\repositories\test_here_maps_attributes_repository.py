from typing import Any, List, Optional, Set
from unittest.mock import Mock, patch

import pytest
from requests.exceptions import RequestException

from logistiscore.ir.here_response import (
    HEREAttributesResponse,
    HERERowData,
    HERETileData,
)
from logistiscore.ir.tile_layer_mapping import TileLayerMapping
from logistiscore.repositories.here_maps_attributes_repository import (
    HEREMapsAttributesRepository,
    compute_slope_data,
)
from logistiscore.tests.factories import (
    create_slope_segment_measurement,
    create_territory,
)
from logistiscore.workers.here_tile_computation import HERETileComputationWorker

TEST_SLOPE_PERCENTAGE_VALUES = {
    859: 1.5,
    2291: 4.0,
    2862: 5.0,
    3715: 6.5,
}


def approximate_slope_percentage(millideg: int):  # pragma: no cover
    return pytest.approx(TEST_SLOPE_PERCENTAGE_VALUES.get(millideg, 0.0), abs=0.1)


@pytest.fixture
def sample_tile_mappings() -> Set[TileLayerMapping]:
    return {
        TileLayerMapping(tile_id=12345, layer="ROAD_GEOM_FC1"),
        TileLayerMapping(tile_id=67890, layer="ADAS_ATTRIB_FC3"),
    }


@pytest.fixture
def sample_here_response() -> HEREAttributesResponse:
    response_data = {
        "Tiles": [
            {
                "Rows": [
                    {"LINK_ID": "123", "SLOPES": "859,1432,-571"},
                    {"LINK_ID": "456", "SLOPES": "2862"},
                ]
            }
        ]
    }
    return HEREAttributesResponse.from_dict(response_data)


@pytest.fixture
def empty_here_response() -> HEREAttributesResponse:
    return HEREAttributesResponse.from_dict({"Tiles": []})


class TestFetchTiles:
    @patch("logistiscore.repositories.here_maps_attributes_repository.retry_call")
    def test_fetch_tiles_with_empty_mappings_returns_empty_tiles(
        self, mock_retry_call: Any
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        empty_mappings: Set[TileLayerMapping] = set()

        result = repository._fetch_tiles(empty_mappings)

        assert result.tiles == []
        mock_retry_call.assert_not_called()

    @patch("logistiscore.repositories.here_maps_attributes_repository.retry_call")
    def test_fetch_tiles_calls_retry_with_correct_params(
        self, mock_retry_call: Any, sample_tile_mappings: Set[TileLayerMapping]
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        mock_retry_call.return_value = {"Tiles": []}

        repository._fetch_tiles(sample_tile_mappings)

        mock_retry_call.assert_called_once()
        call_args = mock_retry_call.call_args
        assert call_args[1]["tries"] == repository.MAX_RETRIES
        assert call_args[1]["delay"] == 1
        assert call_args[1]["backoff"] == 2
        assert call_args[1]["exceptions"] == (RequestException,)

    def test_fetch_tiles_retry_succeeds_on_second_attempt_v2(
        self,
        sample_tile_mappings: Set[TileLayerMapping],
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")

        with patch.object(
            repository,
            "_make_request",
            side_effect=[
                RequestException("Network error"),
                {
                    "Tiles": [
                        {
                            "Rows": [
                                {"LINK_ID": "123", "SLOPES": "859,1432,-571"},
                            ]
                        }
                    ]
                },
            ],
        ) as mock_make_request:
            result = repository._fetch_tiles(sample_tile_mappings)

        assert result.tiles == [
            HERETileData(rows=[HERERowData(link_id=123, slopes="859,1432,-571")])
        ]
        assert mock_make_request.call_count == 2


class TestBuildRequestParams:
    def test_build_request_params_creates_correct_structure(self) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        mappings = [
            TileLayerMapping(tile_id=12345, layer="ROAD_GEOM_FC1"),
            TileLayerMapping(tile_id=67890, layer="ADAS_ATTRIB_FC3"),
        ]

        params = repository._build_request_params(mappings)

        assert params == {
            "apiKey": "test_api_key",
            "in": "tile:12345,67890",
            "layers": "ROAD_GEOM_FC1,ADAS_ATTRIB_FC3",
        }


class TestMakeRequest:
    @patch("logistiscore.repositories.here_maps_attributes_repository.requests.get")
    def test_make_request_should_return_json(self, mock_get: Any) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"Tiles": [{"SLOPES": "data"}]}
        mock_get.return_value = mock_response
        params = {"in": "params"}

        result = repository._make_request(params)

        assert result == {"Tiles": [{"SLOPES": "data"}]}
        mock_get.assert_called_once_with(
            repository.BASE_URL, params=params, timeout=repository.TIMEOUT_SECONDS
        )

    @patch("logistiscore.repositories.here_maps_attributes_repository.requests.get")
    def test_make_request_with_404_should_return_empty_tiles(
        self, mock_get: Any
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        mock_response = Mock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response
        params = {"in": "params"}

        result = repository._make_request(params)

        assert result == {"Tiles": []}

    @patch("logistiscore.repositories.here_maps_attributes_repository.requests.get")
    def test_make_request_error_status_raises_exception(self, mock_get: Any) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_get.return_value = mock_response
        params = {"test": "params"}

        with pytest.raises(
            RequestException, match="Failed to fetch tiles: 500 - Internal Server Error"
        ):
            repository._make_request(params)


class TestExtractSlopeDataFromResponse:
    def test__extract_slope_data_from_response_with_valid_data_should_return_segments(
        self, sample_here_response: HEREAttributesResponse
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        target_segments = [123, 456]

        result = repository._extract_slope_data_from_response(
            sample_here_response, target_segments
        )

        expected_segments = {
            123: create_slope_segment_measurement(
                segment_id=123, slope_percentage=approximate_slope_percentage(2291)
            ),
            456: create_slope_segment_measurement(
                segment_id=456, slope_percentage=approximate_slope_percentage(2862)
            ),
        }
        assert expected_segments == {segment.segment_id: segment for segment in result}

    def test__extract_slope_data_from_response_filters_target_segments(
        self, sample_here_response: HEREAttributesResponse
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        target_segments = [123]

        result = repository._extract_slope_data_from_response(
            sample_here_response, target_segments
        )

        expected_segments = {
            123: create_slope_segment_measurement(
                segment_id=123, slope_percentage=approximate_slope_percentage(2291)
            )
        }
        assert expected_segments == {segment.segment_id: segment for segment in result}

    def test__extract_slope_data_from_response_with_empty_response_should_be_empty_data(
        self, empty_here_response: HEREAttributesResponse
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        target_segments = [123, 456]

        result = repository._extract_slope_data_from_response(
            empty_here_response, target_segments
        )

        assert len(result) == 0

    def test__extract_slope_data_from_response_with_no_target_segments_should_be_empty(
        self, sample_here_response: HEREAttributesResponse
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        target_segments: List[int] = []

        result = repository._extract_slope_data_from_response(
            sample_here_response, target_segments
        )

        assert len(result) == 0

    def test__extract_slope_data_from_response_with_missing_tiles_key(self):
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        response_data = HEREAttributesResponse.from_dict({})
        target_segments = [123]

        result = repository._extract_slope_data_from_response(
            response_data, target_segments
        )

        assert len(result) == 0

    def test_extract_slope_segments_from_tile_with_none_segment_should_skip(
        self,
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        here_row_data = HERERowData(link_id=123, slopes="859,1432,-571")
        tile = HERETileData(rows=[here_row_data])
        target_link_set = {123}
        with patch.object(
            repository, "_parse_max_slope_segment_from_row", return_value=None
        ) as mock_parse:
            result = repository._extract_slope_segments_from_tile(tile, target_link_set)

            assert len(result) == 0
            mock_parse.assert_called_once_with(here_row_data)


class TestParseMaxSlopeSegmentFromRow:
    def test_parse_max_slope_segment_from_row_with_multiple_slopes(self) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        row = HERERowData(link_id=123, slopes="859,1432,-571")

        result = repository._parse_max_slope_segment_from_row(row)

        assert result == create_slope_segment_measurement(
            segment_id=123, slope_percentage=approximate_slope_percentage(2291)
        )

    def test_parse_max_slope_segment_from_row_with_empty_slopes_should_be_none(
        self,
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        row = HERERowData(link_id=123, slopes="")

        result = repository._parse_max_slope_segment_from_row(row)

        assert result is None

    def test_parse_max_slope_segment_from_row_with_missing_slopes_key_should_be_none(
        self,
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        row = HERERowData(link_id=123, slopes=None)

        result = repository._parse_max_slope_segment_from_row(row)

        assert result is None

    def test_parse_max_slope_segment_from_row_with_unknown_slopes_should_return_none_for_slope(
        self,
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        row = HERERowData(link_id=123, slopes="-0,-0,-0")

        result = repository._parse_max_slope_segment_from_row(row)

        expected_segment = create_slope_segment_measurement(
            segment_id=123, slope_percentage=None
        )
        assert result == expected_segment

    def test_parse_max_slope_segment_from_row_should_compute_slope_even_with_unknown_slope(
        self,
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        row = HERERowData(link_id=123, slopes="859,-0,1432")

        result = repository._parse_max_slope_segment_from_row(row)

        expected_segment = create_slope_segment_measurement(
            segment_id=123, slope_percentage=approximate_slope_percentage(2291)
        )
        assert result == expected_segment

    def test_parse_max_slope_segment_from_row_with_no_slope_values_should_return_none(
        self,
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        row = HERERowData(link_id=123, slopes="859,1432")
        with patch.object(
            repository, "_parse_slope_values_from_string", return_value=[]
        ):
            result = repository._parse_max_slope_segment_from_row(row)
            assert result is None

    def test_parse_max_slope_segment_from_row_with_invalid_slopes_should_return_none(
        self,
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        row = HERERowData(link_id=123, slopes="bad")
        with patch.object(
            repository, "_parse_slope_values_from_string", side_effect=ValueError
        ):
            result = repository._parse_max_slope_segment_from_row(row)
            assert result is None


class TestParseSlopeValuesFromString:
    def test_parse_slope_values_from_string_with_relative_values_should_sum_correctly(
        self,
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        slopes_str = "859,1432,-571"

        result = repository._parse_slope_values_from_string(slopes_str)

        assert result == [859, 2291, 1720]

    def test_parse_slope_values_from_string_with_unknown_values_should_have_none(
        self,
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        slopes_str = "859,-0,571"

        result = repository._parse_slope_values_from_string(slopes_str)

        assert result == [859, None, 1430]

    def test_parse_slope_values_from_string_with_empty_string(self) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        slopes_str = ""

        result = repository._parse_slope_values_from_string(slopes_str)

        assert result == []

    def test_parse_slope_values_from_string_with_whitespace(self) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        slopes_str = " 859 , 1432 , -571 "

        result = repository._parse_slope_values_from_string(slopes_str)

        assert result == [859, 2291, 1720]

    @patch("logistiscore.repositories.here_maps_attributes_repository.logging")
    def test_parse_slope_values_from_string_with_invalid_format_should_append_none(
        self, mock_logging: Any
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        slopes_str = "859,abc,1432"

        result = repository._parse_slope_values_from_string(slopes_str)

        assert result == [859, None, 2291]
        mock_logging.warning.assert_called_once_with(
            "Invalid slope value 'abc' in SLOPES string: 859,abc,1432"
        )


class TestFindMaxAbsoluteSlope:
    def test_find_max_absolute_slope_with_valid_values(self) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        slope_values: List[Optional[int]] = [859, 2291, -1720]

        result = repository._find_max_absolute_slope(slope_values)

        assert result == 2291

    def test_find_max_absolute_slope_with_negative_max(self) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        slope_values: List[Optional[int]] = [859, -2291, 1720]

        result = repository._find_max_absolute_slope(slope_values)

        assert result == -2291

    def test_find_max_absolute_slope_with_none_values(self) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        slope_values: List[Optional[int]] = [859, None, 2291]

        result = repository._find_max_absolute_slope(slope_values)

        assert result == 2291

    def test_find_max_absolute_slope_with_all_none_values(self) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        slope_values: List[Optional[int]] = [None, None, None]

        result = repository._find_max_absolute_slope(slope_values)

        assert result is None

    def test_find_max_absolute_slope_with_empty_list(self) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        slope_values: List[Optional[int]] = []

        result = repository._find_max_absolute_slope(slope_values)

        assert result is None


class TestCreateSlopeSegment:
    @pytest.mark.parametrize(
        "slope_millideg,expected_percentage",
        [
            (859, approximate_slope_percentage(859)),
            (2291, approximate_slope_percentage(2291)),
            (2862, approximate_slope_percentage(2862)),
            (3715, approximate_slope_percentage(3715)),
            (-2291, approximate_slope_percentage(2291)),
        ],
    )
    def test_create_slope_segment_with_test_values(
        self, slope_millideg: int, expected_percentage: float
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        segment_id = 123

        result = repository._create_slope_segment(slope_millideg, segment_id)

        assert result == create_slope_segment_measurement(
            segment_id=segment_id,
            slope_percentage=expected_percentage,
        )

    def test_create_slope_segment_with_zero_slope(self) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        slope_millideg = 0
        segment_id = 123

        result = repository._create_slope_segment(slope_millideg, segment_id)

        assert result == create_slope_segment_measurement(
            segment_id=segment_id, slope_percentage=0.0
        )

    @pytest.mark.parametrize(
        "slope_millideg",
        [
            90000,
            -90000,
            100000,
        ],
    )
    def test_create_slope_segment_with_extreme_slope_should_return_max_percentage(
        self, slope_millideg: int
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        segment_id = 123

        result = repository._create_slope_segment(slope_millideg, segment_id)

        assert result == create_slope_segment_measurement(
            segment_id=segment_id, slope_percentage=11459156.0
        )


@patch("logistiscore.repositories.here_maps_attributes_repository.requests.get")
def test_fetch_and_extract_slope_data_should_return_correct_segments(
    mock_get: Any,
) -> None:
    repository = HEREMapsAttributesRepository(api_key="test_api_key")
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "Tiles": [
            {
                "Rows": [
                    {"LINK_ID": "123", "SLOPES": "859,1432"},
                    {"LINK_ID": "999", "SLOPES": "2862"},
                ]
            }
        ]
    }
    mock_get.return_value = mock_response

    mappings = {TileLayerMapping(tile_id=12345, layer="SLOPES_FC1")}
    target_segments = [123]

    tiles_response = repository._fetch_tiles(mappings)
    slope_data = repository._extract_slope_data_from_response(
        tiles_response, target_segments
    )

    expected_segments = {
        123: create_slope_segment_measurement(
            segment_id=123, slope_percentage=approximate_slope_percentage(2291)
        )
    }
    assert expected_segments == {segment.segment_id: segment for segment in slope_data}


def test_compute_slope_data_should_return_frozen_set_of_measurements() -> None:
    here_maps_repository = HEREMapsAttributesRepository("test_api_key")
    tile_worker = HERETileComputationWorker()
    segments = [1000, 2000]
    territory = create_territory()
    mock_response = HEREAttributesResponse(
        tiles=[
            HERETileData(
                rows=[
                    HERERowData(link_id=1000, slopes="859,1432,-571"),
                    HERERowData(link_id=2000, slopes="2862"),
                ]
            )
        ]
    )
    expected_measurements = frozenset(
        [
            create_slope_segment_measurement(segment_id=1000, slope_percentage=5.0),
            create_slope_segment_measurement(segment_id=2000, slope_percentage=10.0),
        ]
    )

    with (
        patch.object(here_maps_repository, "_fetch_tiles", return_value=mock_response),
        patch.object(
            here_maps_repository,
            "_extract_slope_data_from_response",
            return_value=expected_measurements,
        ),
    ):
        result = compute_slope_data(
            here_maps_repository, segments, territory, tile_worker
        )

    assert result == expected_measurements


def test_compute_slope_data_should_return_empty_frozen_set_when_no_tiles(
    empty_here_response: HEREAttributesResponse,
) -> None:
    here_maps_repository = HEREMapsAttributesRepository("test_api_key")
    tile_worker = HERETileComputationWorker()
    segments = [1000, 2000]
    territory = create_territory()

    with patch.object(
        here_maps_repository, "_fetch_tiles", return_value=empty_here_response
    ):
        result = compute_slope_data(
            here_maps_repository, segments, territory, tile_worker
        )

    assert result == frozenset()


def test_compute_slope_data_should_return_empty_frozen_set_when_no_tiles_in_response(
    empty_here_response: HEREAttributesResponse,
) -> None:
    here_maps_repository = HEREMapsAttributesRepository("test_api_key")
    tile_worker = HERETileComputationWorker()
    segments = [1000, 2000]
    territory = create_territory()
    mock_response = empty_here_response

    with patch.object(here_maps_repository, "_fetch_tiles", return_value=mock_response):
        result = compute_slope_data(
            here_maps_repository, segments, territory, tile_worker
        )

    assert result == frozenset()
