LogistiScore
------------

A delivery index analysis tool that evaluates and calculates scores for road networks.

Description
-----------

LogistiScore analyzes speed data of road segments and calculates delivery scores for different types of vehicles
(heavy goods vehicles, light commercial vehicles, cargo bikes),
providing a detailed evaluation of deliverability on road networks.

For detailed specifications, please refer to [https://citecch.sharepoint.com/:w:/s/BIBLIO/EaQneDkatnhPpRCLSFxXYOoBS_JT-4OhOFyTmL52UhdQ4A?e=CGeX3m].

Requirements
------------

- Python >= 3.11
- Dependencies listed in ``pyproject.toml``

Installation
------------

Simply use `pip install -e .[test,lint]`

Input data
------------------

LogistiScore expects input data to be organized in the following directory structure:

::

  territories_dir/
  ├── territory_1/
  │   ├── perimeter.geojson
  │   ├── segments.geojson (or streets.shp, etc.)
  │   ├── speed_data.csv (or fcd_data.csv, etc.)
  │   └── slope_data.csv (optional)
  └── territory_2/
    ├── perimeter.geojson
    ├── segments.geojson
    ├── speed_data.csv
    └── slope_data.csv (optional)

Where:
- ``perimeter.geojson``: Defines the geographical boundary of the territory
- ``segments.geojson``: Contains road network segments to be analyzed
- ``speed_data.csv``: Contains speed measurements for the segments
- ``slope_data.csv``: Contains cached slope measurements for the segments from the HERE API (optional)

Each territory is processed independently, allowing for analysis of multiple regions.
When using the command-line interface, you can specify filenames in the passed directory.

Usage
-----

::

  ```python -m logistiscore.entry_points.run_logistiscore_workflow \
    --territories-dir [TERRITORIES_FOLDER_PATH] \
    --here-api-key [HERE_API_KEY] \
    --segments-filename [SEGMENTS_FILENAME] \
    --speed-data-filename [SPEED_DATA_FILENAME] \
    --perimeter-filename [PERIMETER_FILENAME] \
    --output-network [OUTPUT_FOLDER_PATH] \
    --criteria-types C5 \
    --start-date [START_DATE_YYYY-MM-DD] \
    --end-date [END_DATE_YYYY-MM-DD] \
    --start-epoch [START_EPOCH] \
    --end-epoch [END_EPOCH]
    ```
