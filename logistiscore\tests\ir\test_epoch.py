import pytest

from logistiscore.ir.epoch import EpochType


class TestEpochType:
    @pytest.mark.parametrize(
        ["epoch_type"],
        [[epoch_type] for epoch_type in EpochType if epoch_type != EpochType.UNKNOWN],
    )
    def test_should_get_code_for_all_epoch_types(self, epoch_type: EpochType) -> None:
        code = epoch_type.get_code()

        assert isinstance(code, str)
        assert code == epoch_type.value

    @pytest.mark.parametrize(
        ["epoch_string", "epoch_type"],
        [
            ("EPOCH-15MIN", EpochType.EPOCH_15MIN),
            ("EPOCH-5MIN", EpochType.EPOCH_5MIN),
            ("EPOCH-60MIN", EpochType.EPOCH_60MIN),
        ],
    )
    def test_should_parse_string_for_all_epoch_types(
        self, epoch_string: str, epoch_type: EpochType
    ) -> None:
        parsed_epoch_type = EpochType.from_string(epoch_string)

        assert parsed_epoch_type == epoch_type

    def test_from_string_returns_unknown_for_invalid_epoch_type(self) -> None:
        unknown_epoch = EpochType.from_string("Invalid epoch")

        assert unknown_epoch == EpochType.UNKNOWN
