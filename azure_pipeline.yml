trigger: none
pool:
  vmImage: ubuntu-latest
strategy:
  matrix:
    Python310:
      python.version: '3.11'

variables:
  - name: pip_cache_dir
    value: $(Pipeline.Workspace)/.pip

steps:
- task: UsePythonVersion@0
  inputs:
    versionSpec: '$(python.version)'
  displayName: 'Use Python $(python.version)'

- script: |
    sudo apt update
  displayName: 'Install system dependencies'

- script: |
    python -m pip install --upgrade pip
  displayName: 'Install pip'

- task: Cache@2
  inputs:
    key: 'pip | "$(Agent.OS)" | pyproject.toml'
    restoreKeys: |
      pip | "$(Agent.OS)"
    path: $(pip_cache_dir)
  displayName: 'Restore pip cache'

- script: |
    pip install .[test,lint] -U --upgrade-strategy eager --cache-dir $(pip_cache_dir)
  displayName: 'Install dependencies'

- script: |
    isort . --check
  displayName: 'Sort imports'

- script: |
    black --check --diff -t py311 --exclude="venv|alembic|.eggs" .
  displayName: 'Lint'

- script: |
    flake8
  displayName: 'Check syntax'

- script: |
    mypy
  displayName: 'Check typing'

- script: |
    pip install pytest pytest-azurepipelines
    pytest --cov --cov-branch --cov-fail-under=100 --cov-report=term-missing:skip-covered
  displayName: 'Test'
