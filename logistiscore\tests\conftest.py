import json
import shutil
from pathlib import Path

import geopandas as gpd
import pytest
from shapely.geometry import LineString


@pytest.fixture
def territory_structure(tmp_path: Path) -> Path:
    territories_dir = tmp_path / "territories"
    territories_dir.mkdir()

    territory1_dir = territories_dir / "territory1"
    territory1_dir.mkdir()

    territory2_dir = territories_dir / "territory2"
    territory2_dir.mkdir()

    (territories_dir / "not_a_directory.txt").write_text("This is not a directory")

    return territories_dir


def create_perimeter_file(territory_dir: Path, perimeter_type: str) -> None:
    if perimeter_type == "valid":
        geojson_data = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
                    },
                    "properties": {},
                }
            ],
        }
    elif perimeter_type == "multiple_geometries":
        geojson_data = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
                    },
                    "properties": {},
                },
                {
                    "type": "Feature",
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[[2, 2], [3, 2], [3, 3], [2, 3], [2, 2]]],
                    },
                    "properties": {},
                },
            ],
        }
    elif perimeter_type == "point_geometry":
        geojson_data = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": {
                        "type": "Point",
                        "coordinates": [0, 0],
                    },
                    "properties": {},
                }
            ],
        }
    elif perimeter_type == "empty_polygon":
        geojson_data = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[]],
                    },
                    "properties": {},
                }
            ],
        }
    else:
        raise ValueError(f"Unsupported perimeter type: {perimeter_type}")

    perimeter_file = territory_dir / "perimeter.geojson"
    perimeter_file.write_text(json.dumps(geojson_data))


def territory_factory(
    territory_structure: Path,
    territory_name: str = "territory1",
    perimeter_type: str = "valid",
) -> Path:
    territory_dir = territory_structure / territory_name
    create_perimeter_file(territory_dir, perimeter_type)
    return territory_structure


@pytest.fixture
def valid_territory(territory_structure: Path) -> Path:
    return territory_factory(territory_structure, perimeter_type="valid")


@pytest.fixture
def multiple_geometries_territory(territory_structure: Path) -> Path:
    return territory_factory(territory_structure, perimeter_type="multiple_geometries")


@pytest.fixture
def point_geometry_territory(territory_structure: Path) -> Path:
    return territory_factory(territory_structure, perimeter_type="point_geometry")


@pytest.fixture
def empty_polygon_territory(territory_structure: Path) -> Path:
    return territory_factory(territory_structure, perimeter_type="empty_polygon")


@pytest.fixture
def two_valid_territories(territory_structure: Path) -> Path:
    territory_factory(territory_structure, "territory1", "valid")
    territory_factory(territory_structure, "territory2", "valid")
    return territory_structure


@pytest.fixture
def here_fcd_file(tmp_path: Path) -> str:
    fcd_file = tmp_path / "test_fcd.csv"
    fcd_file.write_text(
        "LINK-DIR,MEAN,COUNT,FREEFLOW,DATE-TIME,EPOCH-60MIN\n"
        "10000F,35.5,10,45.0,2025-05-16 08:00,6\n"
        "10000F,32.0,15,45.0,2025-05-16 09:00,7\n"
        "20000F,28.5,8,40.0,2025-05-16 08:00,8\n"
        "20000F,31.0,12,40.0,2025-05-16 09:00,9\n"
    )
    return str(fcd_file)


@pytest.fixture
def here_segments_file(tmp_path: Path) -> str:
    segments_file = tmp_path / "test_segments.geojson"

    gdf = gpd.GeoDataFrame(
        {
            "LINK_ID": [10000, 20000],
            "ST_NAME": ["coco 1", "coco 2"],
            "DIR_TRAVEL": ["F", "T"],
            "geometry": [
                LineString([(0, 0), (1, 1)]),
                LineString([(0, 0), (2, 2)]),
            ],
        }
    )

    gdf.to_file(segments_file, driver="GeoJSON")
    return str(segments_file)


@pytest.fixture
def territory_with_data_files(
    tmp_path: Path, here_fcd_file: str, here_segments_file: str
) -> Path:
    territories_dir = tmp_path / "territories"
    territories_dir.mkdir()

    territory_dir = territories_dir / "test_territory"
    territory_dir.mkdir()

    shutil.copy(here_fcd_file, territory_dir / "speed_data.csv")
    shutil.copy(here_segments_file, territory_dir / "segments.geojson")

    (territory_dir / "slope_data.csv").write_text("header1,header2\n")

    create_perimeter_file(territory_dir, "valid")

    return territories_dir
