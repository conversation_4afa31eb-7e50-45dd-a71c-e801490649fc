from typing import Dict, FrozenSet, Optional, Tuple

from shapely.geometry import LineString, Polygon

from logistiscore.ir.criteria import CriteriaType, Threshold
from logistiscore.ir.epoch import EpochType
from logistiscore.ir.network import (
    CriteriaGrade,
    Network,
    Segment,
    SegmentScore,
    TerritoryCriteriaScore,
    TerritoryScore,
)
from logistiscore.ir.slope import SlopeSegmentMeasurement
from logistiscore.ir.speed import AveragedSegmentSpeedMeasurement, AveragedSpeedData
from logistiscore.ir.territory import Territory, TerritoryData
from logistiscore.ir.vehicle import VehicleType


def create_averaged_segment_speed_measurement(
    segment_id: int = 10000,
    epoch: int = 1,
    avg_speed: float = 30.0,
    total_count: int = 10,
    freeflow: float = 50.0,
    direction: str = "F",
) -> AveragedSegmentSpeedMeasurement:
    return AveragedSegmentSpeedMeasurement(
        segment_id=segment_id,
        epoch=epoch,
        avg_speed=avg_speed,
        total_count=total_count,
        freeflow=freeflow,
        direction=direction,
    )


def create_averaged_speed_data(
    measurements: Tuple[AveragedSegmentSpeedMeasurement, ...] = (
        create_averaged_segment_speed_measurement(),
    ),
    epoch_type: EpochType = EpochType.EPOCH_15MIN,
) -> AveragedSpeedData:
    return AveragedSpeedData(measurements=measurements, epoch_type=epoch_type)


def create_threshold(good: float = 80.0, bad: float = 20.0) -> Threshold:
    return Threshold(good=good, bad=bad)


def create_segment(
    segment_id: int = 10000,
    name: str = "Rue coco",
    geometry: LineString = LineString([(0, 0), (1, 1)]),
    length: float = 1.0,
    direction: Optional[str] = None,
) -> Segment:
    return Segment(
        segment_id=segment_id,
        name=name,
        geometry=geometry,
        length=length,
        direction=direction,
    )


def create_criteria_grade(
    segment_id: int = 10000,
    criteria_type: CriteriaType = CriteriaType.C5,
    epoch: Optional[int] = 1,
    vehicle_type: VehicleType = VehicleType.VUL,
    grade: float = 75.0,
    direction: Optional[str] = None,
) -> CriteriaGrade:
    return CriteriaGrade(
        segment_id=segment_id,
        criteria_type=criteria_type,
        epoch=epoch,
        vehicle_type=vehicle_type,
        grade=grade,
        direction=direction,
    )


def create_segment_score(
    segment_id: int = 10000,
    epoch: int = 1,
    vehicle_type: VehicleType = VehicleType.VUL,
    score: float = 80.0,
    normalized_score: float = 75.0,
    direction: Optional[str] = None,
) -> SegmentScore:
    return SegmentScore(
        segment_id=segment_id,
        epoch=epoch,
        vehicle_type=vehicle_type,
        score=score,
        normalized_score=normalized_score,
        direction=direction,
    )


def create_territory_criteria_score(
    criteria_type: CriteriaType = CriteriaType.C5,
    epoch: int = 1,
    vehicle_type: VehicleType = VehicleType.VUL,
    score: float = 80.0,
    normalized_score: float = 75.0,
) -> TerritoryCriteriaScore:
    return TerritoryCriteriaScore(
        criteria_type=criteria_type,
        epoch=epoch,
        vehicle_type=vehicle_type,
        score=score,
        normalized_score=normalized_score,
    )


def create_territory_score(
    epoch: int = 1,
    vehicle_type: VehicleType = VehicleType.VUL,
    score: float = 80.0,
    normalized_score: float = 75.0,
) -> TerritoryScore:
    return TerritoryScore(
        epoch=epoch,
        vehicle_type=vehicle_type,
        score=score,
        normalized_score=normalized_score,
    )


def create_network(
    segments: Dict[int, Segment] = {10000: create_segment(segment_id=10000)},
    segment_scores: FrozenSet[SegmentScore] = frozenset([create_segment_score()]),
    territory_criteria_scores: FrozenSet[TerritoryCriteriaScore] = frozenset(
        [create_territory_criteria_score()]
    ),
    territory_overall_scores: FrozenSet[TerritoryScore] = frozenset(
        [create_territory_score()]
    ),
    segment_criteria_grades: FrozenSet[CriteriaGrade] = frozenset(
        [create_criteria_grade()]
    ),
) -> Network:
    return Network(
        segments=segments,
        segment_scores=segment_scores,
        territory_criteria_scores=territory_criteria_scores,
        territory_overall_scores=territory_overall_scores,
        segment_criteria_grades=segment_criteria_grades,
    )


def create_territory(
    name: str = "Geneva",
    geometry: Polygon = Polygon([(0, 0), (1, 0), (1, 1), (0, 1), (0, 0)]),
) -> Territory:
    return Territory(name=name, geometry=geometry)


def create_slope_segment_measurement(
    segment_id: int = 10000,
    slope_percentage: Optional[float] = 5.0,
) -> SlopeSegmentMeasurement:
    return SlopeSegmentMeasurement(
        segment_id=segment_id,
        slope_percentage=slope_percentage,
    )


def create_territory_data(
    segments: Dict[int, Segment] = {10000: create_segment(segment_id=10000)},
    segment_weights: Dict[int, float] = {10000: 1.0},
    speed_data: AveragedSpeedData = create_averaged_speed_data(),
    territory: Territory = create_territory(),
    slope_data: FrozenSet[SlopeSegmentMeasurement] = frozenset(
        [create_slope_segment_measurement()]
    ),
) -> TerritoryData:
    return TerritoryData(
        territory=territory,
        segments=segments,
        segment_weights=segment_weights,
        speed_data=speed_data,
        slope_data=slope_data,
    )
