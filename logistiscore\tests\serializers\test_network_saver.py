import os
from unittest.mock import Mock, patch

import geopandas as gpd
import pandas as pd

from logistiscore.serializers.network_saver import (
    add_datetime_from_epoch,
    convert_criteria_grades_to_pivot_df,
    convert_segments_to_gdf,
    make_dir,
    save_network,
    save_to_csv,
    save_to_geo<PERSON><PERSON>,
)
from logistiscore.tests.factories import (
    create_criteria_grade,
    create_network,
    create_segment,
)


class TestNetworkSaver:

    def test_make_dir_creates_directory_if_not_exists(self):
        with (
            patch("os.path.exists") as mock_exists,
            patch("os.makedirs") as mock_makedirs,
        ):

            mock_exists.return_value = False
            make_dir("test/path")
            mock_exists.assert_called_once_with("test/path")
            mock_makedirs.assert_called_once_with("test/path")

    def test_make_dir_does_nothing_if_directory_exists(self):
        with (
            patch("os.path.exists") as mock_exists,
            patch("os.makedirs") as mock_makedirs,
        ):

            mock_exists.return_value = True
            make_dir("test/path")
            mock_exists.assert_called_once_with("test/path")
            mock_makedirs.assert_not_called()

    def test_convert_segments_to_gdf_formats_correctly(self):
        segment1 = create_segment(segment_id=10000, name="rue 1")
        segment2 = create_segment(segment_id=20000, name="rue 2")
        network = create_network(segments={10000: segment1, 20000: segment2})

        result = convert_segments_to_gdf(network)

        assert isinstance(result, gpd.GeoDataFrame)
        assert len(result) == 2
        assert "geometry" in result.columns
        assert "segment_id" in result.columns
        assert "name" in result.columns
        assert "length" in result.columns
        street1_row = result[result["segment_id"] == 10000].iloc[0]
        street2_row = result[result["segment_id"] == 20000].iloc[0]
        assert street1_row["name"] == "rue 1"
        assert street2_row["name"] == "rue 2"

    def test_save_to_csv_uses_pandas_to_csv(self):
        df = pd.DataFrame([{"key1": "value1"}, {"key1": "value2"}])
        file_path = "output/file.csv"

        with patch.object(pd.DataFrame, "to_csv") as mock_to_csv:
            save_to_csv(df, file_path)
            mock_to_csv.assert_called_once_with(file_path, index=False)

    def test_save_to_geojson_uses_geopandas_to_file(self):
        gdf_mock = Mock(spec=gpd.GeoDataFrame)
        file_path = "output/file.geojson"

        save_to_geojson(gdf_mock, file_path)

        gdf_mock.to_file.assert_called_once_with(file_path, driver="GeoJSON")

    @patch("logistiscore.serializers.network_saver.make_dir")
    @patch("logistiscore.serializers.network_saver.save_to_geojson")
    @patch("logistiscore.serializers.network_saver.save_to_csv")
    def test_save_network_creates_all_files(
        self, mock_save_csv, mock_save_geojson, mock_make_dir
    ):
        segment1 = create_segment(segment_id=10000, name="rue 1")
        segment2 = create_segment(segment_id=20000, name="rue 2")
        network = create_network(segments={10000: segment1, 20000: segment2})

        save_network(network, "output/folder", "territory1")

        mock_make_dir.assert_called_once_with(
            os.path.join("output/folder", "territory1")
        )
        mock_save_geojson.assert_called_once()
        gdf = mock_save_geojson.call_args[0][0]
        assert isinstance(gdf, gpd.GeoDataFrame)
        assert len(gdf) == 2
        assert set(gdf["segment_id"].tolist()) == {10000, 20000}
        assert mock_save_geojson.call_args[0][1] == os.path.join(
            "output/folder", "territory1", "segments.geojson"
        )
        assert mock_save_csv.call_count == 3

    def test_add_datetime_from_epoch_should_add_datetime_column(self):
        test_data = pd.DataFrame(
            {
                "criteria_type": ["C5", "C5"],
                "epoch": [9, 7],
                "vehicle_type": ["Vélo-cargo", "Poids lourd"],
                "score": [2.5238021856925985, 2.088357623583563],
                "normalized_score": [87.03095406725858, 47.71348903396085],
            }
        )

        result = add_datetime_from_epoch(test_data, "epoch", "hour")

        assert "hour" in result.columns
        assert len(result) == 2
        assert isinstance(result["hour"].iloc[0], pd.Timestamp)

    def test_convert_criteria_grades_to_pivot_df_should_pivot_criteria_grades(self):
        from logistiscore.ir.criteria import CriteriaType

        criteria_grades = frozenset(
            [
                create_criteria_grade(
                    segment_id=10000, criteria_type=CriteriaType.C5, grade=2.5
                ),
                create_criteria_grade(
                    segment_id=10000, criteria_type=CriteriaType.C11, grade=3.0
                ),
                create_criteria_grade(
                    segment_id=20000, criteria_type=CriteriaType.C5, grade=1.5
                ),
            ]
        )
        network = create_network(segment_criteria_grades=criteria_grades)

        result = convert_criteria_grades_to_pivot_df(network)

        assert len(result) == 2  # Two segments
        assert "segment_id" in result.columns
        assert "score_criteria_c5" in result.columns
        assert "score_criteria_c11" in result.columns

        # Check segment 10000 has both criteria
        segment_10000 = result[result["segment_id"] == 10000].iloc[0]
        assert segment_10000["score_criteria_c5"] == 2.5
        assert segment_10000["score_criteria_c11"] == 3.0

        # Check segment 20000 has only C5 criteria
        segment_20000 = result[result["segment_id"] == 20000].iloc[0]
        assert segment_20000["score_criteria_c5"] == 1.5
        assert pd.isna(segment_20000["score_criteria_c11"])

    def test_convert_criteria_grades_to_pivot_df_empty_criteria_should_return_empty_df(
        self,
    ):
        network = create_network(segment_criteria_grades=frozenset())

        result = convert_criteria_grades_to_pivot_df(network)

        assert result.empty

    def test_save_network_integrates_criteria_grades_into_segments_gdf(self):
        from logistiscore.ir.criteria import CriteriaType

        segment1 = create_segment(segment_id=10000, name="rue 1")
        segment2 = create_segment(segment_id=20000, name="rue 2")
        criteria_grades = frozenset(
            [
                create_criteria_grade(
                    segment_id=10000, criteria_type=CriteriaType.C5, grade=2.5
                ),
                create_criteria_grade(
                    segment_id=10000, criteria_type=CriteriaType.C11, grade=3.0
                ),
                create_criteria_grade(
                    segment_id=20000, criteria_type=CriteriaType.C5, grade=1.5
                ),
            ]
        )
        network = create_network(
            segments={10000: segment1, 20000: segment2},
            segment_criteria_grades=criteria_grades,
        )

        with patch(
            "logistiscore.serializers.network_saver.save_to_geojson"
        ) as mock_save_geojson:
            with patch("logistiscore.serializers.network_saver.save_to_csv"):
                with patch("logistiscore.serializers.network_saver.make_dir"):
                    save_network(network, "output/folder", "territory1")

        # Check that the GeoDataFrame passed to save_to_geojson contains criteria columns
        gdf = mock_save_geojson.call_args[0][0]
        assert "score_criteria_c5" in gdf.columns
        assert "score_criteria_c11" in gdf.columns

        # Check that segment 10000 has both criteria values
        segment_10000_row = gdf[gdf["segment_id"] == 10000].iloc[0]
        assert segment_10000_row["score_criteria_c5"] == 2.5
        assert segment_10000_row["score_criteria_c11"] == 3.0

        # Check that segment 20000 has only C5 criteria
        segment_20000_row = gdf[gdf["segment_id"] == 20000].iloc[0]
        assert segment_20000_row["score_criteria_c5"] == 1.5
        assert pd.isna(segment_20000_row["score_criteria_c11"])

    def test_add_datetime_from_epoch_when_column_missing_returns_unchanged_df(self):
        test_data = pd.DataFrame(
            {
                "criteria_type": ["C5", "C5"],
                "other_column": [9, 7],
                "vehicle_type": ["Vélo-cargo", "Poids lourd"],
                "score": [2.5, 2.0],
            }
        )

        result = add_datetime_from_epoch(test_data, "epoch", "hour")

        assert "hour" not in result.columns
        assert len(result) == 2
        assert result.equals(test_data)
