from pathlib import Path
from typing import Any
from unittest.mock import patch

import pytest

from logistiscore.serializers.cache_slope_data import (
    load_slope_data_from_cache,
    save_slope_data_to_cache,
)
from logistiscore.tests.factories import create_slope_segment_measurement


def test_load_slope_data_from_cache_should_return_frozen_set_of_measurements(
    tmp_path: Path,
):
    slope_data = frozenset(
        {
            create_slope_segment_measurement(segment_id=1, slope_percentage=5.0),
            create_slope_segment_measurement(segment_id=2, slope_percentage=10.0),
        }
    )
    cache_file = tmp_path / "slope_data.csv"
    cache_file.write_text(
        "segment_id,slope_percentage\n1,5.0\n2,10.0\n", encoding="utf-8"
    )

    result = load_slope_data_from_cache(str(tmp_path), "slope_data.csv")

    assert result == slope_data


def test_load_slope_data_from_cache_should_raise_file_not_found_error(tmp_path: Path):
    with pytest.raises(FileNotFoundError):
        load_slope_data_from_cache(str(tmp_path), "non_existent_file.csv")


@patch("logistiscore.serializers.cache_slope_data.pd.read_csv")
def test_load_slope_data_from_cache_should_raise_value_error_on_invalid_data(
    mock_read_csv: Any, tmp_path: Path
):
    cache_file = tmp_path / "slope_data.csv"
    cache_file.write_text(
        "segment_id,slope_percentage\n1,5.0\n2,10.0\n", encoding="utf-8"
    )
    mock_read_csv.side_effect = ValueError("Invalid CSV format")

    with pytest.raises(ValueError):
        load_slope_data_from_cache(str(tmp_path), "slope_data.csv")


def test_save_slope_data_to_cache_should_create_file_with_correct_data(tmp_path: Path):
    slope_data = frozenset(
        {
            create_slope_segment_measurement(segment_id=1, slope_percentage=5.0),
            create_slope_segment_measurement(segment_id=2, slope_percentage=10.0),
        }
    )
    territory_dir = str(tmp_path)

    save_slope_data_to_cache(territory_dir, slope_data, "slope_data.csv")

    cache_file = tmp_path / "slope_data.csv"
    assert cache_file.exists()
    content = cache_file.read_text(encoding="utf-8").strip().split("\n")
    expected_content = [
        "segment_id,slope_percentage",
        "1,5.0",
        "2,10.0",
    ]
    assert set(content) == set(expected_content)


def test_save_slope_data_to_cache_should_create_directory_if_not_exists(tmp_path: Path):
    slope_data = frozenset(
        {
            create_slope_segment_measurement(segment_id=1, slope_percentage=5.0),
        }
    )
    territory_dir = str(tmp_path / "territory")

    save_slope_data_to_cache(territory_dir, slope_data, "slope_data.csv")

    cache_file = Path(territory_dir) / "slope_data.csv"
    assert cache_file.exists()
    content = cache_file.read_text(encoding="utf-8")
    expected_content = "segment_id,slope_percentage\n1,5.0\n"
    assert content == expected_content
