from enum import Enum


class VehicleType(Enum):
    PL = "PL"
    VUL = "VUL"
    VC = "VC"

    def translate(self):
        match self:
            case VehicleType.PL:
                return "Poids lourd"
            case VehicleType.VUL:
                return "Véhicule utilitaire léger"
            case VehicleType.VC:
                return "Vélo-cargo"
            case _:
                raise ValueError(f"Unknown vehicle type: {self}")

    @staticmethod
    def parse_french(vehicle: str) -> "VehicleType":
        return {
            "Poids lourd": VehicleType.PL,
            "Véhicule utilitaire léger": VehicleType.VUL,
            "Vélo-cargo": VehicleType.VC,
        }[vehicle]
