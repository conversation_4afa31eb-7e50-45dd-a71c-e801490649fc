from dataclasses import dataclass
from typing import Any, Dict, List, Optional


@dataclass(frozen=True)
class HERERowData:
    link_id: Optional[int]
    slopes: Optional[str]

    @classmethod
    def from_dict(cls, row_data: Dict[str, Any]) -> "HERERowData":
        link_id = row_data.get("LINK_ID")
        if link_id is not None:
            try:
                link_id = int(link_id)
            except ValueError:
                raise ValueError(f"Invalid LINK_ID value: {link_id}")
        return cls(
            link_id=link_id,
            slopes=row_data.get("SLOPES"),
        )


@dataclass(frozen=True)
class HERETileData:
    rows: List[HERERowData]

    @classmethod
    def from_dict(cls, tile_data: Dict[str, Any]) -> "HERETileData":
        rows = [HERERowData.from_dict(row) for row in tile_data.get("Rows", [])]
        return cls(rows=rows)


@dataclass(frozen=True)
class HEREAttributesResponse:
    tiles: List[HERETileData]

    @classmethod
    def from_dict(cls, response_data: Dict[str, Any]) -> "HEREAttributesResponse":
        tiles = [
            HERETileData.from_dict(tile) for tile in response_data.get("Tiles", [])
        ]
        return cls(tiles=tiles)
