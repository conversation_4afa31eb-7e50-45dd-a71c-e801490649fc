from typing import Dict
from unittest.mock import Mock

import pandas as pd
import pytest
from pandas.testing import assert_frame_equal

from logistiscore.calculators.criteria_calculator import CriteriaCalculatorFactory
from logistiscore.calculators.network_score_calculator import NetworkScoreCalculator
from logistiscore.ir.criteria import CriteriaType
from logistiscore.ir.network import Network
from logistiscore.ir.vehicle import VehicleType
from logistiscore.tests.factories import (
    create_averaged_speed_data,
    create_criteria_grade,
    create_segment,
    create_territory_data,
    create_threshold,
)


class TestNetworkScoreCalculator:
    def test_collect_segment_criteria_grades_should_compute_grades(
        self,
    ) -> None:
        strategy_factory = Mock(spec=CriteriaCalculatorFactory)
        strategy = Mock()
        strategy_factory.get_calculator.return_value = strategy
        segments = {10000: create_segment(segment_id=10000)}
        speed_data = create_averaged_speed_data()
        criteria_types = [CriteriaType.C5]
        criteria_grades = {
            create_criteria_grade(segment_id=10000, criteria_type=CriteriaType.C5)
        }
        strategy.calculate.return_value = criteria_grades
        threshold = create_threshold()
        thresholds_config = {CriteriaType.C5: {VehicleType.PL: threshold}}
        calculator = NetworkScoreCalculator(strategy_factory, thresholds_config)
        territory_data = create_territory_data(segments=segments, speed_data=speed_data)

        result = calculator._collect_segment_criteria_grades(
            territory_data, criteria_types
        )

        strategy_factory.get_calculator.assert_called_once_with(CriteriaType.C5)
        strategy.calculate.assert_called_once_with(
            territory_data, thresholds_config[CriteriaType.C5]
        )
        expected_data = frozenset(criteria_grades)
        assert result == expected_data

    def test__convert_criteria_grades_to_dataframe_should_convert_to_dataframe(
        self,
    ) -> None:
        strategy_factory = Mock(spec=CriteriaCalculatorFactory)
        thresholds_config = {CriteriaType.C5: {VehicleType.PL: create_threshold()}}
        calculator = NetworkScoreCalculator(strategy_factory, thresholds_config)
        criteria_grades = frozenset(
            [create_criteria_grade(segment_id=10000, criteria_type=CriteriaType.C5)]
        )

        result = calculator._convert_criteria_grades_to_dataframe(criteria_grades)

        expected_data = pd.DataFrame(
            [criteria_grade.to_dict() for criteria_grade in criteria_grades]
        )
        assert_frame_equal(result, expected_data)

    def test_calculate_segment_scores_should_compute_scores(
        self,
    ) -> None:
        strategy_factory = Mock(spec=CriteriaCalculatorFactory)
        thresholds_config = {CriteriaType.C5: {VehicleType.PL: create_threshold()}}
        calculator = NetworkScoreCalculator(strategy_factory, thresholds_config)
        segment_criteria_df = pd.DataFrame(
            [
                {
                    "segment_id": 10000,
                    "criteria_type": "C5",
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "grade": 3.0,
                },
                {
                    "segment_id": 10000,
                    "criteria_type": "C5",
                    "epoch": 1,
                    "vehicle_type": "Véhicule utilitaire léger",
                    "grade": 2.0,
                },
                {
                    "segment_id": 20000,
                    "criteria_type": "C5",
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "grade": 1.0,
                },
            ]
        )
        criteria_weights = {CriteriaType.C5: 1.0}

        result = calculator._calculate_segment_scores(
            segment_criteria_df, criteria_weights
        )

        expected_data = pd.DataFrame(
            [
                {
                    "segment_id": 10000,
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "score": 3.0,
                    "sum_of_weights": 1.0,
                    "weighted_avg_score": 3.0,
                    "normalized_score": 100.0,
                },
                {
                    "segment_id": 10000,
                    "epoch": 1,
                    "vehicle_type": "Véhicule utilitaire léger",
                    "score": 2.0,
                    "sum_of_weights": 1.0,
                    "weighted_avg_score": 2.0,
                    "normalized_score": 50.0,
                },
                {
                    "segment_id": 20000,
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "score": 1.0,
                    "sum_of_weights": 1.0,
                    "weighted_avg_score": 1.0,
                    "normalized_score": 0.0,
                },
            ]
        )
        assert_frame_equal(result, expected_data)

    def test_calculate_segment_scores_with_equal_scores_should_return_50_as_normalized(
        self,
    ) -> None:
        strategy_factory = Mock(spec=CriteriaCalculatorFactory)
        thresholds_config = {CriteriaType.C5: {VehicleType.PL: create_threshold()}}
        calculator = NetworkScoreCalculator(strategy_factory, thresholds_config)
        segment_criteria_df = pd.DataFrame(
            [
                {
                    "segment_id": 10000,
                    "criteria_type": "C5",
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "grade": 3.0,
                },
                {
                    "segment_id": 20000,
                    "criteria_type": "C5",
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "grade": 3.0,
                },
            ]
        )
        criteria_weights = {CriteriaType.C5: 1.0}

        result = calculator._calculate_segment_scores(
            segment_criteria_df, criteria_weights
        )

        assert len(result) == 2
        assert result.iloc[0]["normalized_score"] == 50
        assert result.iloc[1]["normalized_score"] == 50

    def test_calculate_criteria_scores(self) -> None:
        strategy_factory = Mock(spec=CriteriaCalculatorFactory)
        thresholds_config = {CriteriaType.C5: {VehicleType.PL: create_threshold()}}
        calculator = NetworkScoreCalculator(strategy_factory, thresholds_config)
        segment_criteria_df = pd.DataFrame(
            [
                {
                    "segment_id": 10000,
                    "criteria_type": "C5",
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "grade": 3.0,
                },
                {
                    "segment_id": 20000,
                    "criteria_type": "C5",
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "grade": 2.0,
                },
            ]
        )
        segment_weights = {10000: 2.0, 20000: 1.0}

        result = calculator._calculate_criteria_scores(
            segment_criteria_df, segment_weights
        )

        expected_data = pd.DataFrame(
            [
                {
                    "criteria_type": "C5",
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "score": (3.0 * 2.0 + 2.0 * 1.0) / (2.0 + 1.0),
                    "total_weight": 3.0,
                    "normalized_score": 50.0,
                }
            ]
        )
        assert_frame_equal(result, expected_data)

    def test_calculate_criteria_scores_empty_result(self) -> None:
        strategy_factory = Mock(spec=CriteriaCalculatorFactory)
        thresholds_config = {CriteriaType.C5: {VehicleType.PL: create_threshold()}}
        calculator = NetworkScoreCalculator(strategy_factory, thresholds_config)
        segment_criteria_df = pd.DataFrame(
            columns=["segment_id", "criteria_type", "epoch", "vehicle_type", "grade"]
        )
        segment_weights = {10000: 2.0, 20000: 1.0}

        result = calculator._calculate_criteria_scores(
            segment_criteria_df, segment_weights
        )

        assert_frame_equal(
            result,
            pd.DataFrame(
                columns=[
                    "criteria_type",
                    "epoch",
                    "vehicle_type",
                    "score",
                    "total_weight",
                ]
            ),
        )
        assert "normalized_score" not in result.columns

    def test_normalize_criteria_scores_should_scale_scores(self) -> None:
        strategy_factory = Mock(spec=CriteriaCalculatorFactory)
        thresholds_config = {CriteriaType.C5: {VehicleType.PL: create_threshold()}}
        calculator = NetworkScoreCalculator(strategy_factory, thresholds_config)
        criteria_scores_df = pd.DataFrame(
            [
                {
                    "criteria_type": "C5",
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "score": 2.0,
                    "total_weight": 3.0,
                },
                {
                    "criteria_type": "C5",
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "score": 3.0,
                    "total_weight": 3.0,
                },
                {
                    "criteria_type": "C5",
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "score": 1.0,
                    "total_weight": 3.0,
                },
            ]
        )

        result = calculator._normalize_criteria_scores(criteria_scores_df)

        assert len(result) == 3
        assert result["normalized_score"].iloc[0] == 50.0
        assert result["normalized_score"].iloc[1] == 100.0
        assert result["normalized_score"].iloc[2] == 0.0

    def test_normalize_criteria_scores_all_equal(self) -> None:
        strategy_factory = Mock(spec=CriteriaCalculatorFactory)
        thresholds_config = {CriteriaType.C5: {VehicleType.PL: create_threshold()}}
        calculator = NetworkScoreCalculator(strategy_factory, thresholds_config)
        criteria_scores_df = pd.DataFrame(
            [
                {
                    "criteria_type": "C5",
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "score": 2.0,
                    "total_weight": 3.0,
                },
                {
                    "criteria_type": "C5",
                    "epoch": 2,
                    "vehicle_type": "Poids lourd",
                    "score": 2.0,
                    "total_weight": 3.0,
                },
            ]
        )

        result = calculator._normalize_criteria_scores(criteria_scores_df)

        assert result["normalized_score"].iloc[0] == 50
        assert result["normalized_score"].iloc[1] == 50

    def test_calculate_territory_overall_scores(self) -> None:
        strategy_factory = Mock(spec=CriteriaCalculatorFactory)
        thresholds_config = {CriteriaType.C5: {VehicleType.PL: create_threshold()}}
        calculator = NetworkScoreCalculator(strategy_factory, thresholds_config)
        criteria_scores_df = pd.DataFrame(
            [
                {
                    "criteria_type": "C5",
                    "epoch": 1,
                    "vehicle_type": "Poids lourd",
                    "score": 2.0,
                    "total_weight": 3.0,
                    "normalized_score": 70.0,
                },
            ]
        )
        criteria_weights = {CriteriaType.C5: 1.0}

        result = calculator._calculate_territory_overall_scores(
            criteria_scores_df, criteria_weights
        )

        assert len(result) == 1
        territory_score = result.iloc[0]
        assert territory_score["epoch"] == 1
        assert territory_score["vehicle_type"] == "Poids lourd"
        assert territory_score["weighted_sum"] == 70.0
        assert territory_score["weight_sum"] == 1.0
        assert territory_score["score"] == 70.0
        assert territory_score["normalized_score"] == 70.0

    @pytest.mark.parametrize(
        "segment_weights",
        [None, {10000: 2.0, 20000: 1.0}],
    )
    def test_calculate_network_returns_proper_network(
        self, segment_weights: Dict[int, float]
    ) -> None:
        strategy_factory = Mock(spec=CriteriaCalculatorFactory)
        strategy = Mock()
        strategy_factory.get_calculator.return_value = strategy
        segments = {
            10000: create_segment(segment_id=10000),
            20000: create_segment(segment_id=20000),
        }
        speed_data = create_averaged_speed_data()
        criteria_types = [CriteriaType.C5]
        criteria_weights = {CriteriaType.C5: 1.0}
        criteria_grades = [
            create_criteria_grade(
                segment_id=10000, criteria_type=CriteriaType.C5, grade=3.0
            ),
            create_criteria_grade(
                segment_id=20000, criteria_type=CriteriaType.C5, grade=1.0
            ),
        ]
        strategy.calculate.return_value = criteria_grades
        threshold = create_threshold()
        thresholds_config = {CriteriaType.C5: {VehicleType.PL: threshold}}
        territory_data = create_territory_data(
            segments=segments, speed_data=speed_data, segment_weights=segment_weights
        )
        calculator = NetworkScoreCalculator(strategy_factory, thresholds_config)

        network = calculator.calculate_network(
            territory_data, criteria_types, criteria_weights
        )

        assert isinstance(network, Network)
        assert set(network.segments.keys()) == {10000, 20000}
        assert len(network.segment_scores) == 2
        assert len(network.territory_criteria_scores) == 1
        assert len(network.territory_overall_scores) == 1
        segment_scores = sorted(
            list(network.segment_scores), key=lambda x: x.segment_id
        )
        assert segment_scores[0].segment_id == 10000
        assert segment_scores[1].segment_id == 20000
        territory_criteria_score = next(iter(network.territory_criteria_scores))
        assert territory_criteria_score.criteria_type == CriteriaType.C5
        territory_overall_score = next(iter(network.territory_overall_scores))
        assert territory_overall_score.score > 0
