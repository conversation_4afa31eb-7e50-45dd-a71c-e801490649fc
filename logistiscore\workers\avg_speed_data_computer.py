from collections import defaultdict
from typing import Callable, List

from logistiscore.ir.speed import AveragedSegmentSpeedMeasurement, AveragedSpeedData


def min_segment_speed_aggregator() -> (
    Callable[[List[AveragedSegmentSpeedMeasurement]], AveragedSegmentSpeedMeasurement]
):
    def aggregate(
        measurements: List[AveragedSegmentSpeedMeasurement],
    ) -> AveragedSegmentSpeedMeasurement:
        if len(measurements) == 1:
            return measurements[0]
        min_measurement = min(measurements, key=lambda m: m.avg_speed)

        return AveragedSegmentSpeedMeasurement(
            segment_id=min_measurement.segment_id,
            epoch=min_measurement.epoch,
            avg_speed=min_measurement.avg_speed,
            total_count=sum(m.total_count for m in measurements),
            freeflow=min_measurement.freeflow,
            direction=min_measurement.direction,
        )

    return aggregate


def aggregate_directions_by_segment_epoch(
    speed_data: AveragedSpeedData,
    aggregator: Callable[
        [List[AveragedSegmentSpeedMeasurement]], AveragedSegmentSpeedMeasurement
    ] = min_segment_speed_aggregator(),
) -> AveragedSpeedData:
    grouped = defaultdict(list)
    for measurement in speed_data.measurements:
        grouped[(measurement.segment_id, measurement.epoch)].append(measurement)

    return AveragedSpeedData(
        measurements=tuple(aggregator(group) for group in grouped.values()),
        epoch_type=speed_data.epoch_type,
    )
