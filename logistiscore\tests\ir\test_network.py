from logistiscore.ir.criteria import CriteriaType
from logistiscore.ir.vehicle import VehicleType
from logistiscore.tests.factories import (
    create_criteria_grade,
    create_segment_score,
    create_territory_criteria_score,
    create_territory_score,
)


class TestCriteriaGrade:
    def test_should_convert_to_dict(self):
        criteria_grade = create_criteria_grade(
            segment_id=10000,
            criteria_type=CriteriaType.C5,
            epoch=1,
            vehicle_type=VehicleType.VUL,
            grade=80.0,
        )

        dict_data = criteria_grade.to_dict()

        assert dict_data == {
            "segment_id": 10000,
            "criteria_type": "C5",
            "epoch": 1,
            "vehicle_type": "Véhicule utilitaire léger",
            "grade": 80.0,
        }


class TestSegmentScore:
    def test_should_convert_to_dict(self):
        segment_score = create_segment_score(
            segment_id=10000,
            epoch=1,
            vehicle_type=VehicleType.VUL,
            score=80.0,
            normalized_score=75.0,
        )

        dict_data = segment_score.to_dict()

        assert dict_data == {
            "segment_id": 10000,
            "epoch": 1,
            "vehicle_type": "Véhicule utilitaire léger",
            "score": 80.0,
            "normalized_score": 75.0,
        }


class TestTerritoryCriteriaScore:
    def test_should_convert_to_dict(self):
        territory_criteria_score = create_territory_criteria_score(
            criteria_type=CriteriaType.C5,
            epoch=1,
            vehicle_type=VehicleType.VUL,
            score=80.0,
            normalized_score=75.0,
        )

        dict_data = territory_criteria_score.to_dict()

        assert dict_data == {
            "criteria_type": "C5",
            "epoch": 1,
            "vehicle_type": "Véhicule utilitaire léger",
            "score": 80.0,
            "normalized_score": 75.0,
        }


class TestTerritoryScore:
    def test_should_convert_to_dict(self):
        territory_score = create_territory_score(
            epoch=1, vehicle_type=VehicleType.VUL, score=80.0, normalized_score=75.0
        )

        dict_data = territory_score.to_dict()

        assert dict_data == {
            "epoch": 1,
            "vehicle_type": "Véhicule utilitaire léger",
            "score": 80.0,
            "normalized_score": 75.0,
        }
