import os
from datetime import date

import geopandas as gpd
import pandas as pd

from logistiscore.ir.network import Network


def make_dir(folder_path: str) -> None:
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)


def convert_criteria_grades_to_pivot_df(network: Network) -> pd.DataFrame:
    criteria_records = [grade.to_dict() for grade in network.segment_criteria_grades]
    criteria_df = pd.DataFrame(criteria_records)

    pivot_df = criteria_df.pivot_table(
        index="segment_id",
        columns="criteria_type",
        values="grade",
        aggfunc="first",
    )
    pivot_df.columns = pd.Index(
        [f"score_criteria_{col.lower()}" for col in pivot_df.columns]
    )
    pivot_df = pivot_df.reset_index()

    return pivot_df


def convert_segments_to_gdf(network: Network) -> gpd.GeoDataFrame:

    features = []
    for segment_id, segment in network.segments.items():
        features.append(
            {
                "geometry": segment.geometry,
                "segment_id": segment_id,
                "name": segment.name,
                "length": segment.length,
            }
        )

    gdf = gpd.GeoDataFrame(features, geometry="geometry")
    return gdf


def save_to_csv(df: pd.DataFrame, file_path: str) -> None:
    df.to_csv(file_path, index=False)


def save_to_geojson(gdf: gpd.GeoDataFrame, file_path: str) -> None:
    gdf.to_file(file_path, driver="GeoJSON")


def save_network(network: Network, folder_path: str, territory_name: str) -> None:
    territory_folder_path = os.path.join(folder_path, territory_name)
    make_dir(territory_folder_path)
    segments_gdf = convert_segments_to_gdf(network)
    segment_score_df = pd.DataFrame(
        [score.to_dict() for score in network.segment_scores]
    )
    segment_score_df = add_datetime_from_epoch(
        segment_score_df,
        epoch_column="epoch",
        datetime_column="hour",
    )
    criteria_scores_df = pd.DataFrame(
        [score.to_dict() for score in network.territory_criteria_scores]
    )
    criteria_scores_df = add_datetime_from_epoch(
        criteria_scores_df,
        epoch_column="epoch",
        datetime_column="hour",
    )
    network_score_df = pd.DataFrame(
        [score.to_dict() for score in network.territory_overall_scores]
    )
    network_score_df = add_datetime_from_epoch(
        network_score_df,
        epoch_column="epoch",
        datetime_column="hour",
    )
    criteria_grades_pivot_df = convert_criteria_grades_to_pivot_df(network)

    joined_segments_data = segments_gdf.join(
        segment_score_df.set_index("segment_id"), on="segment_id"
    )

    joined_segments_data = joined_segments_data.merge(
        criteria_grades_pivot_df, on="segment_id", how="left"
    )

    save_to_geojson(
        joined_segments_data, os.path.join(territory_folder_path, "segments.geojson")
    )
    save_to_csv(
        segment_score_df, os.path.join(territory_folder_path, "segment_scores.csv")
    )
    save_to_csv(
        criteria_scores_df,
        os.path.join(territory_folder_path, "territory_criteria_scores.csv"),
    )
    save_to_csv(
        network_score_df,
        os.path.join(territory_folder_path, "territory_overall_scores.csv"),
    )


def add_datetime_from_epoch(
    df: pd.DataFrame,
    epoch_column: str,
    datetime_column: str = "hour",
) -> pd.DataFrame:
    new_df = df.copy()
    if epoch_column in new_df.columns:
        reference_date = date(2025, 5, 23)  # Fixed reference date: May 23, 2025
        new_df[datetime_column] = pd.to_datetime(reference_date) + pd.to_timedelta(
            new_df[epoch_column], unit="h"
        )
    return new_df
