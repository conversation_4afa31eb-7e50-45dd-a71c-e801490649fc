# HEREMapsAttributesRepository Refactoring Plan

## Overview
Refactor the `HEREMapsAttributesRepository` class to improve encapsulation and add proper response parsing with dataclasses. This is a breaking change refactor that removes public access to internal methods.

## Goals
- ✅ Make `fetch_tiles` and `extract_slope_data_from_response` private methods
- ✅ Add structured response parsing with dataclasses (already created)
- ✅ Create new public method `get_maps_attribute_data` as single entry point
- ✅ Keep `SlopeSegmentMeasurement` creation logic in repository
- ✅ Clean, focused public API with proper encapsulation

## Current State Analysis

### Public Methods (will become private)
- `fetch_tiles(mappings: Set[TileLayerMapping]) -> Dict[str, List[Dict]]`
- `extract_slope_data_from_response(response_data: Dict, target_segments: List[int]) -> FrozenSet[SlopeSegmentMeasurement]`

### Private Methods (remain private)
- `_build_request_params`
- `_make_request`
- `_extract_slope_segments_from_tile`
- `_parse_max_slope_segment_from_row`
- `_parse_slope_values_from_string`
- `_find_max_absolute_slope`
- `_create_slope_segment`

### External Function
- `compute_slope_data()` - will be simplified

### New Public API
- `get_maps_attribute_data(mappings: Set[TileLayerMapping], target_segments: List[int]) -> FrozenSet[SlopeSegmentMeasurement]`

## Implementation Steps

### Step 1: Response Dataclasses (Already Created)
**File**: `logistiscore/ir/here_response.py` ✅

The dataclasses are already created with clean structure:
- `HERERowData`: Represents individual row with link_id and slopes
- `HERETileData`: Contains list of rows from a tile
- `HEREAttributesResponse`: Top-level response containing tiles

**Note**: No helper methods in dataclasses - all filtering and processing logic stays in repository.

### Step 2: Refactor Repository Class
**File**: `logistiscore/repositories/here_maps_attributes_repository.py`

#### 2.1 Update Imports
```python
# Add new import
from logistiscore.ir.here_response import HEREAttributesResponse
```

#### 2.2 Make Public Methods Private and Update Return Types
```python
def _fetch_tiles(self, mappings: Set[TileLayerMapping]) -> HEREAttributesResponse:
    """Private method - use get_maps_attribute_data instead."""
    if len(mappings) == 0:
        return HEREAttributesResponse.from_dict({"Tiles": []})

    sorted_mappings = sorted(mappings, key=lambda m: (m.layer, m.tile_id))
    params = self._build_request_params(sorted_mappings)

    response_data = retry_call(
        lambda: self._make_request(params),
        tries=self.MAX_RETRIES,
        delay=1,
        backoff=2,
        exceptions=(RequestException,),
    )

    return HEREAttributesResponse.from_dict(response_data)

def _extract_slope_data_from_response(
    self, response: HEREAttributesResponse, target_segments: List[int]
) -> FrozenSet[SlopeSegmentMeasurement]:
    target_link_set = set(segment_id for segment_id in target_segments)
    slope_data = set()

    for tile in response.tiles:
        slope_segments = self._extract_slope_segments_from_tile_data(tile, target_link_set)
        slope_data.update(slope_segments)

    return frozenset(slope_data)
```

#### 2.3 Add New Public Method
```python
def get_maps_attribute_data(
    self, mappings: Set[TileLayerMapping], target_segments: List[int]
) -> FrozenSet[SlopeSegmentMeasurement]:
    response = self._fetch_tiles(mappings)
    return self._extract_slope_data_from_response(response, target_segments)
```

#### 2.4 Update Tile Processing Methods
```python
def _extract_slope_segments_from_tile_data(
    self, tile: HERETileData, target_link_set: Set[int]
) -> Set[SlopeSegmentMeasurement]:
    slope_segments = set()

    for row in tile.rows:
        if row.link_id is not None and row.link_id in target_link_set:
            segment = self._parse_max_slope_segment_from_row_data(row)
            if segment is not None:
                slope_segments.add(segment)

    return slope_segments

def _parse_max_slope_segment_from_row_data(
    self, row: HERERowData
) -> Optional[SlopeSegmentMeasurement]:
    if not row.slopes or row.link_id is None:
        return None

    try:
        slope_values = self._parse_slope_values_from_string(row.slopes)
        if not slope_values:
            return None

        max_slope = self._find_max_absolute_slope(slope_values)
        if max_slope is None:
            return SlopeSegmentMeasurement(
                segment_id=row.link_id,
                slope_percentage=None,
            )

        return self._create_slope_segment(max_slope, row.link_id)
    except ValueError:
        return None
```

### Step 3: Update External Function
**File**: `logistiscore/repositories/here_maps_attributes_repository.py`

```python
def compute_slope_data(
    repository: HEREMapsAttributesRepository,
    segments: List[int],
    territory: Territory,
    tile_worker: HERETileComputationWorker,
) -> FrozenSet[SlopeSegmentMeasurement]:
    """Compute slope data for segments in a territory."""
    tile_mappings = tile_worker.compute_tiles_for_territory(territory)
    return repository.get_maps_attribute_data(tile_mappings, segments)
```

**Benefits**:
- Simplified logic (no more response validation)
- Delegates to repository's public method
- Clean separation of concerns

### Step 4: Update Tests
**File**: `logistiscore/tests/repositories/test_here_maps_attributes_repository.py`

#### 4.1 Update Imports
```python
# Update imports to include new response classes
from logistiscore.ir.here_response import HEREAttributesResponse, HERETileData, HERERowData
```

#### 4.2 Update Test Fixtures to Use Dataclasses
```python
@pytest.fixture
def sample_here_response() -> HEREAttributesResponse:
    response_data = {
        "Tiles": [
            {
                "Rows": [
                    {"LINK_ID": "123", "SLOPES": "859,1432,-571"},
                    {"LINK_ID": "456", "SLOPES": "2862"},
                ]
            }
        ]
    }
    return HEREAttributesResponse.from_dict(response_data)
```

#### 4.3 Replace Tests for Old Public Methods with New Method
```python
class TestGetMapsAttributeData:
    def test_get_maps_attribute_data_returns_slope_measurements(
        self, sample_tile_mappings: Set[TileLayerMapping]
    ) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        target_segments = [123, 456]

        with patch.object(repository, '_fetch_tiles') as mock_fetch:
            mock_response = create_mock_here_response()
            mock_fetch.return_value = mock_response

            result = repository.get_maps_attribute_data(sample_tile_mappings, target_segments)

            assert isinstance(result, frozenset)
            assert len(result) > 0

    def test_get_maps_attribute_data_with_empty_mappings_returns_empty_set(self) -> None:
        repository = HEREMapsAttributesRepository(api_key="test_api_key")
        empty_mappings: Set[TileLayerMapping] = set()
        target_segments = [123]

        result = repository.get_maps_attribute_data(empty_mappings, target_segments)

        assert result == frozenset()
```

#### 4.4 Remove Tests for Old Public Methods
- Remove `TestFetchTiles` class
- Remove `TestExtractSlopeDataFromResponse` class
- Keep tests for private methods if they test internal logic

## Implementation Timeline

### Single Phase: Breaking Changes Implementation
- ✅ Use existing `here_response.py` dataclasses
- ✅ Update repository to use structured responses
- ✅ Make existing public methods private
- ✅ Add new public method `get_maps_attribute_data`
- ✅ Update `compute_slope_data` function
- ✅ Update all tests to use new API
- ✅ Update any imports in other files

## Benefits

- ✅ Clean, focused public API with single entry point
- ✅ Better encapsulation with private methods
- ✅ Structured data representation with type safety
- ✅ Cleaner separation of concerns (parsing vs. business logic)
- ✅ Easier to extend and maintain
- ✅ Better testability with structured data

## Files to Modify

1. **Existing**: `logistiscore/ir/here_response.py` ✅ (already created)
2. **Modified**: `logistiscore/repositories/here_maps_attributes_repository.py`
3. **Modified**: `logistiscore/tests/repositories/test_here_maps_attributes_repository.py`
4. **Check**: `logistiscore/workers/territory_builder.py` (import updates if needed)

## Implementation Checklist

- [x] `here_response.py` dataclasses created
- [ ] Update repository imports
- [ ] Make `fetch_tiles` and `extract_slope_data_from_response` private
- [ ] Add `get_maps_attribute_data` public method
- [ ] Update tile processing methods to work with dataclasses
- [ ] Update `compute_slope_data` function
- [ ] Update test imports and fixtures
- [ ] Replace old method tests with new method tests
- [ ] Verify all tests pass
- [ ] Update any external imports

## Success Criteria

- ✅ Repository has single public method `get_maps_attribute_data`
- ✅ All internal methods are private
- ✅ Structured response parsing with dataclasses
- ✅ All tests pass with new API
- ✅ `compute_slope_data` function works with new repository API
- ✅ Code maintains same functionality with better structure
