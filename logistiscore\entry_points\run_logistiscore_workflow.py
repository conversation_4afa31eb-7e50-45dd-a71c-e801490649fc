import argparse
import logging
import sys
from datetime import datetime
from typing import List, Optional

from logistiscore.calculators.criteria_calculator import CriteriaCalculatorFactory
from logistiscore.calculators.network_score_calculator import NetworkScoreCalculator
from logistiscore.constants import CRITERIA_THRESHOLDS, CRITERIA_WEIGHTS
from logistiscore.ir.criteria import CriteriaType
from logistiscore.ir.epoch import EpochInterval
from logistiscore.serializers.network_saver import save_network
from logistiscore.workers.territory_builder import TerritoryBuilder


def run_workflow(
    territories_dir: str,
    here_api_key: str,
    output_network_folder: str,
    criteria_types: List[CriteriaType],
    segments_filename: str,
    speed_data_filename: str,
    perimeter_filename: str,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    epoch_interval: Optional[EpochInterval] = None,
) -> None:
    logging.info("Initializing territory builder")
    territory_builder = TerritoryBuilder(
        territories_dir=territories_dir,
        here_api_key=here_api_key,
        segments_filename=segments_filename,
        speed_data_filename=speed_data_filename,
        perimeter_filename=perimeter_filename,
        start_date=start_date,
        end_date=end_date,
        epoch_interval=epoch_interval,
    )

    calculator_factory = CriteriaCalculatorFactory()
    score_calculator = NetworkScoreCalculator(calculator_factory, CRITERIA_THRESHOLDS)

    for territory_data in territory_builder.build_all_territory_data():
        logging.info(
            f"Calculating network scores for territory {territory_data.territory.name}"
        )
        network = score_calculator.calculate_network(
            territory_data=territory_data,
            criteria_types=criteria_types,
            criteria_weights=CRITERIA_WEIGHTS,
        )

        logging.info(
            f"Calculated {len(network.segment_scores)} segment scores, "
            f"{len(network.territory_overall_scores)} territory scores, "
            f"{len(network.territory_criteria_scores)} territory criteria scores"
        )

        logging.info(f"Saving network for territory {territory_data.territory.name}")
        save_network(network, output_network_folder, territory_data.territory.name)

    logging.info("Workflow completed successfully")


def parse_args(args: Optional[List[str]]) -> argparse.Namespace:
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--territories-dir",
        required=True,
        help="Path to directory with territory perimeters and data",
    )
    parser.add_argument(
        "--here-api-key",
        required=True,
        help="HERE API key for accessing HERE services",
    )
    parser.add_argument(
        "--output-network", required=True, help="Path to output network folder"
    )
    parser.add_argument(
        "--segments-filename",
        default="segments.geojson",
        help="Name of segments file in each territory directory "
        "(default: segments.geojson)",
    )
    parser.add_argument(
        "--speed-data-filename",
        default="speed_data.csv",
        help="Name of speed data file in each territory directory "
        "(default: speed_data.csv)",
    )
    parser.add_argument(
        "--perimeter-filename",
        default="perimeter.geojson",
        help="Name of perimeter file in each territory directory "
        "(default: perimeter.geojson)",
    )
    parser.add_argument(
        "--start-date",
        help="Start date for speed data filtering (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--end-date",
        help="End date for speed data filtering (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--start-epoch",
        help="Start epoch for speed data filtering (epoch format)",
    )
    parser.add_argument(
        "--end-epoch",
        help="End epoch for speed data filtering (epoch format)",
    )
    parser.add_argument(
        "--criteria-types",
        nargs="+",
        default=["C5"],
        help="Criteria types to calculate",
    )
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Logging level",
    )

    return parser.parse_args(args)


def main(args: Optional[List[str]] = None) -> None:
    parsed_args = parse_args(args)

    logging.basicConfig(
        level=getattr(logging, parsed_args.log_level),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    criteria_types = [CriteriaType(ct) for ct in parsed_args.criteria_types]
    start_date = None
    end_date = None
    start_epoch = 0
    end_epoch = 23
    if parsed_args.start_date:
        start_date = datetime.strptime(parsed_args.start_date, "%Y-%m-%d")
    if parsed_args.end_date:
        end_date = datetime.strptime(parsed_args.end_date, "%Y-%m-%d")
    if parsed_args.start_epoch:
        start_epoch = int(parsed_args.start_epoch)
    if parsed_args.end_epoch:
        end_epoch = int(parsed_args.end_epoch)
    epoch_interval = EpochInterval(start_epoch=start_epoch, end_epoch=end_epoch)

    run_workflow(
        territories_dir=parsed_args.territories_dir,
        here_api_key=parsed_args.here_api_key,
        output_network_folder=parsed_args.output_network,
        criteria_types=criteria_types,
        segments_filename=parsed_args.segments_filename,
        speed_data_filename=parsed_args.speed_data_filename,
        perimeter_filename=parsed_args.perimeter_filename,
        start_date=start_date,
        end_date=end_date,
        epoch_interval=epoch_interval,
    )


def execute_script(name: str, args: Optional[List[str]] = None) -> None:
    if name == "__main__":
        try:
            main(args)
        except Exception as e:
            logging.error(e)
            sys.exit(2)


execute_script(__name__)
