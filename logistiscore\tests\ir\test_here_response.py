from typing import Any, Dict, Optional

import pytest

from logistiscore.ir.here_response import HERERowData


@pytest.mark.parametrize(
    "row_data,expected_link_id,expected_slopes",
    [
        ({"LINK_ID": "123", "SLOPES": "859,1432"}, 123, "859,1432"),
        ({"LINK_ID": 456, "SLOPES": None}, 456, None),
        ({}, None, None),
        ({"LINK_ID": None, "SLOPES": "100"}, None, "100"),
    ],
)
def test_hererowdata_from_dict_should_parse_correctly_response(
    row_data: Dict[str, Any],
    expected_link_id: Optional[int],
    expected_slopes: Optional[str],
) -> None:
    row = HERERowData.from_dict(row_data)

    assert row.link_id == expected_link_id
    assert row.slopes == expected_slopes


def test_hererowdata_from_dict_with_invalid_link_id_should_raise_value_error() -> None:
    row_data = {"LINK_ID": "invalid", "SLOPES": "859,1432"}

    with pytest.raises(ValueError, match="Invalid LINK_ID value: invalid"):
        HERERowData.from_dict(row_data)
