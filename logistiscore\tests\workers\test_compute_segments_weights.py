from logistiscore.tests.factories import create_segment
from logistiscore.workers.compute_segments_weights import compute_segments_weights


class TestComputeSegmentsLength:

    def test_compute_segments_length_returns_dictionary(self):
        segment1 = create_segment(segment_id=10000)
        segment2 = create_segment(segment_id=20000)
        segments = {10000: segment1, 20000: segment2}

        result = compute_segments_weights(segments)

        assert result == {
            10000: segment1.length,
            20000: segment2.length,
        }
