from shapely.geometry import Polygon

from logistiscore.ir.territory import GeographicBounds, Territory


def test_bounds_property_should_return_correct_geographic_bounds() -> None:
    geometry = Polygon([(2, 3), (8, 3), (8, 7), (2, 7), (2, 3)])

    territory = Territory(name="Test Territory", geometry=geometry)

    assert territory.bounds == GeographicBounds(
        min_latitude=3.0,
        max_latitude=7.0,
        min_longitude=2.0,
        max_longitude=8.0,
    )
