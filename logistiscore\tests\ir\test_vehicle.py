from unittest.mock import Mock

import pytest

from logistiscore.ir.vehicle import VehicleType


class TestVehicleType:
    @pytest.mark.parametrize(
        ["vehicle_type"],
        [[vehicle_type] for vehicle_type in VehicleType],
    )
    def test_should_translate_to_french_for_all_vehicle_types(
        self,
        vehicle_type: VehicleType,
    ) -> None:
        french_name = vehicle_type.translate()

        assert isinstance(french_name, str)

    def test_translate_raises_value_error_for_unknown_type(self):
        mock_vehicle = Mock(spec=VehicleType)

        with pytest.raises(ValueError):
            VehicleType.translate(mock_vehicle)

    @pytest.mark.parametrize(
        ["french_name", "vehicle_type"],
        [
            ("Poids lourd", VehicleType.PL),
            ("Véhicule utilitaire léger", VehicleType.VUL),
            ("Vélo-cargo", VehicleType.VC),
        ],
    )
    def test_should_parse_french_for_all_vehicle_types(
        self, french_name: str, vehicle_type: VehicleType
    ):
        parsed_vehicle_type = VehicleType.parse_french(french_name)

        assert parsed_vehicle_type == vehicle_type

    def test_parse_french_raises_key_error_for_unknown_vehicle_type(self):
        with pytest.raises(KeyError):
            VehicleType.parse_french("Unknown vehicle")
