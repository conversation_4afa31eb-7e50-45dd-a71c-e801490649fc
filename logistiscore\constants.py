from typing import Dict

from logistiscore.ir.criteria import CriteriaType, Threshold
from logistiscore.ir.vehicle import VehicleType

CRITERIA_WEIGHTS: Dict[CriteriaType, float] = {
    CriteriaType.C5: 1.0,
    CriteriaType.C11: 1.0,
}

C5_THRESHOLDS = {
    VehicleType.PL: Threshold(good=25.0, bad=15.0),
    VehicleType.VUL: Threshold(good=35.0, bad=20.0),
    VehicleType.VC: Threshold(good=15.0, bad=10.0),
}

C11_THRESHOLDS = {
    VehicleType.PL: Threshold(good=3.0, bad=5.0),
    VehicleType.VUL: Threshold(good=6.0, bad=10.0),
    VehicleType.VC: Threshold(good=3.0, bad=6.0),
}

CRITERIA_THRESHOLDS: Dict[CriteriaType, Dict[VehicleType, Threshold]] = {
    CriteriaType.C5: C5_THRESHOLDS,
    CriteriaType.C11: C11_THRESHOLDS,
}
