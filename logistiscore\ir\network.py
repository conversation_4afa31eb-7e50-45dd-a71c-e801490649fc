from dataclasses import dataclass
from typing import Dict, FrozenSet, Optional

from shapely.geometry import LineString

from logistiscore.ir.criteria import CriteriaType
from logistiscore.ir.vehicle import VehicleType


@dataclass(frozen=True, eq=True)
class Segment:
    segment_id: int
    name: str
    geometry: LineString
    length: float
    direction: Optional[str] = None  # direction "F" for forward, "T" for toward


@dataclass(frozen=True, eq=True)
class CriteriaGrade:
    segment_id: int
    criteria_type: CriteriaType
    epoch: Optional[int]
    vehicle_type: VehicleType
    grade: float
    direction: Optional[str] = None

    def to_dict(self) -> dict:
        return {
            "segment_id": self.segment_id,
            "criteria_type": self.criteria_type.get_criteria(),
            "epoch": self.epoch,
            "vehicle_type": self.vehicle_type.translate(),
            "grade": self.grade,
        }


@dataclass(frozen=True, eq=True)
class SegmentScore:
    segment_id: int
    epoch: int
    vehicle_type: VehicleType
    score: float
    normalized_score: float
    direction: Optional[str] = None

    def to_dict(self) -> dict:
        return {
            "segment_id": self.segment_id,
            "epoch": self.epoch,
            "vehicle_type": self.vehicle_type.translate(),
            "score": self.score,
            "normalized_score": self.normalized_score,
        }


@dataclass(frozen=True)
class TerritoryCriteriaScore:
    criteria_type: CriteriaType
    epoch: int
    vehicle_type: VehicleType
    score: float
    normalized_score: float

    def to_dict(self) -> dict:
        return {
            "criteria_type": self.criteria_type.get_criteria(),
            "epoch": self.epoch,
            "vehicle_type": self.vehicle_type.translate(),
            "score": self.score,
            "normalized_score": self.normalized_score,
        }


@dataclass(frozen=True)
class TerritoryScore:
    epoch: int
    vehicle_type: VehicleType
    score: float
    normalized_score: float

    def to_dict(self) -> dict:
        return {
            "epoch": self.epoch,
            "vehicle_type": self.vehicle_type.translate(),
            "score": self.score,
            "normalized_score": self.normalized_score,
        }


@dataclass(frozen=True)
class Network:
    segments: Dict[int, Segment]
    segment_scores: FrozenSet[SegmentScore]
    territory_criteria_scores: FrozenSet[TerritoryCriteriaScore]
    territory_overall_scores: FrozenSet[TerritoryScore]
    segment_criteria_grades: FrozenSet[CriteriaGrade]
