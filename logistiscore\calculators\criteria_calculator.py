from typing import Dict, Protocol, Set

import pandas as pd

from logistiscore.ir.criteria import CriteriaType, Threshold
from logistiscore.ir.network import CriteriaGrade
from logistiscore.ir.territory import TerritoryData
from logistiscore.ir.vehicle import VehicleType


class CriteriaCalculationStrategy(Protocol):

    def calculate(
        self,
        territory_data: TerritoryData,
        thresholds: Dict[VehicleType, Threshold],
    ) -> Set[CriteriaGrade]: ...  # pragma: no cover


class C5SpeedCriteriaCalculationStrategy(CriteriaCalculationStrategy):
    def calculate(
        self,
        territory_data: TerritoryData,
        thresholds: Dict[VehicleType, Threshold],
    ) -> Set[CriteriaGrade]:
        speed_data = territory_data.speed_data
        if len(speed_data.measurements) == 0 or len(thresholds) == 0:
            return set()

        speed_df = pd.DataFrame(
            [
                {"segment_id": m.segment_id, "epoch": m.epoch, "avg_speed": m.avg_speed}
                for m in speed_data.measurements
            ]
        )

        thresholds_df = pd.DataFrame(
            [
                {
                    "vehicle_type": vehicle_type.translate(),
                    "good": thresholds[vehicle_type].good,
                    "bad": thresholds[vehicle_type].bad,
                }
                for vehicle_type in thresholds
            ]
        )

        merged = speed_df.merge(thresholds_df, how="cross")
        merged["value"] = 1.0
        merged.loc[merged["avg_speed"] > merged["bad"], "value"] = 2.0
        merged.loc[merged["avg_speed"] >= merged["good"], "value"] = 3.0

        return {
            CriteriaGrade(
                segment_id=row["segment_id"],
                criteria_type=CriteriaType.C5,
                epoch=row["epoch"],
                vehicle_type=VehicleType.parse_french(row["vehicle_type"]),
                grade=row["value"],
            )
            for _, row in merged.iterrows()
        }


class C11SlopeCriteriaCalculationStrategy(CriteriaCalculationStrategy):
    def calculate(
        self,
        territory_data: TerritoryData,
        thresholds: Dict[VehicleType, Threshold],
    ) -> Set[CriteriaGrade]:
        slope_data = territory_data.slope_data
        if len(slope_data) == 0 or len(thresholds) == 0:
            return set()

        slope_records = [slope.to_dict() for slope in slope_data]

        slope_df = pd.DataFrame(slope_records)

        thresholds_df = pd.DataFrame(
            [
                {
                    "vehicle_type": vehicle_type.translate(),
                    "good_threshold": thresholds[vehicle_type].good,
                    "bad_threshold": thresholds[vehicle_type].bad,
                }
                for vehicle_type in thresholds
            ]
        )

        merged = slope_df.merge(thresholds_df, how="cross")

        # for slope, grading system is as follows:
        # Grade 1: slope >= bad_threshold (worst)
        # Grade 2: good_threshold <= slope < bad_threshold (medium)
        # Grade 3: slope < good_threshold (best)
        merged["grade"] = 1.0  # Default to worst grade
        merged.loc[merged["slope_percentage"] < merged["bad_threshold"], "grade"] = 2.0
        merged.loc[merged["slope_percentage"] < merged["good_threshold"], "grade"] = 3.0

        return {
            CriteriaGrade(
                segment_id=row["segment_id"],
                criteria_type=CriteriaType.C11,
                vehicle_type=VehicleType.parse_french(row["vehicle_type"]),
                grade=row["grade"],
                epoch=None,
            )
            for _, row in merged.iterrows()
        }


class CriteriaCalculatorFactory:
    def __init__(self):
        self._strategies = {
            CriteriaType.C5: C5SpeedCriteriaCalculationStrategy(),
            CriteriaType.C11: C11SlopeCriteriaCalculationStrategy(),
        }

    def get_calculator(
        self, criteria_type: CriteriaType
    ) -> CriteriaCalculationStrategy:
        return self._strategies[criteria_type]
