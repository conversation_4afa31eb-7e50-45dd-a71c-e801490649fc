from datetime import datetime
from typing import List, Optional

import pandas as pd

from logistiscore.ir.epoch import EpochInterval, EpochType
from logistiscore.ir.speed import AveragedSegmentSpeedMeasurement, AveragedSpeedData
from logistiscore.repositories.abstract_repositories import FCDRepository


class HEREFCDRepository(FCDRepository):
    def __init__(self, speed_data_path: str):
        self.df = self._load_speed_data(speed_data_path)
        self.epoch_type = self._get_epoch_type()

    def _get_epoch_type(self) -> EpochType:
        return self.df.attrs.get("epoch_type", EpochType.UNKNOWN)

    def get_averaged_speed_data(
        self,
        segment_ids: Optional[List[int]] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        epoch_interval: Optional[EpochInterval] = None,
    ) -> AveragedSpeedData:
        df = self.df.copy()

        if segment_ids:
            df = df[df["segment_id"].isin(segment_ids)]

        if start_date:
            df = df[df["date"] >= start_date]
        if end_date:
            df = df[df["date"] <= end_date]

        if epoch_interval:
            df = df[
                df["epoch"].between(
                    epoch_interval.start_epoch, epoch_interval.end_epoch
                )
            ]

        agg_df = (
            df.groupby(["segment_id", "epoch", "direction"])
            .agg({"speed": "mean", "count": "sum", "freeflow": "first"})
            .reset_index()
        )

        agg_df.rename(
            columns={"speed": "avg_speed", "count": "total_count"}, inplace=True
        )

        measurements = []
        for _, row in agg_df.iterrows():
            measurements.append(
                AveragedSegmentSpeedMeasurement(
                    segment_id=row["segment_id"],
                    epoch=row["epoch"],
                    avg_speed=row["avg_speed"],
                    total_count=row["total_count"],
                    freeflow=row["freeflow"],
                    direction=row["direction"],
                )
            )

        return AveragedSpeedData(
            measurements=tuple(measurements), epoch_type=self.epoch_type
        )

    def _load_speed_data(self, speed_data_path: str) -> pd.DataFrame:
        epoch_type = self._determine_epoch_type(speed_data_path)
        df = pd.read_csv(speed_data_path)

        df["epoch"] = df[epoch_type.get_code()].astype(int)
        df["date"] = pd.to_datetime(df["DATE-TIME"], format="%Y-%m-%d %H:%M")
        df["LINK-DIR"] = df["LINK-DIR"].astype(str)
        df["segment_id"] = df["LINK-DIR"].str[:-1].astype(int)
        df["direction"] = df["LINK-DIR"].str[-1]
        df["speed"] = df["MEAN"].astype(float)
        df["count"] = df["COUNT"].astype(int)
        df["freeflow"] = df["FREEFLOW"].astype(float)

        df = df[
            ["segment_id", "epoch", "speed", "count", "freeflow", "date", "direction"]
        ]

        df.attrs["epoch_type"] = epoch_type
        return df

    def _determine_epoch_type(self, file_path: str) -> EpochType:
        with open(file_path) as f:
            header = f.readline().strip().split(",")

        for col in header:
            epoch_type = EpochType.from_string(col)
            if epoch_type != EpochType.UNKNOWN:
                return epoch_type

        raise ValueError("No epoch column found in the data")
