{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: logistiscore",
            "type": "debugpy",
            "request": "launch",
            "module": "logistiscore.entry_points.run_logistiscore_workflow",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "args": [
            "--territories-dir", "C:\\Users\\<USER>\\Documents\\affaires\\24M053.0 Indice de livrabilité\\territories",
            "--segments-filename", "geneve_streets.shp",
            "--speed-data-filename", "speed_data.csv",
            "--perimeter-filename", "geneve.geojson",
            "--output-network", "C:\\Users\\<USER>\\Documents\\affaires\\24M053.0 Indice de livrabilité\\results",
            "--criteria-types", "C5",
            "--start-date", "2025-03-01",
            "--end-date", "2025-03-07",
            "--start-epoch", "7",
            "--end-epoch", "8"
            ]
        },
        {
            "name": "Python Debugger: logistiscore (alternate)",
            "type": "debugpy",
            "request": "launch",
            "module": "logistiscore.entry_points.run_logistiscore_workflow",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "args": [
                "--segments", "C:\\Users\\<USER>\\Documents\\affaires\\24M053.0 Indice de livrabilité\\lausanne\\streets\\241G0\\lausanne_streets.shp",
                "--speed-data", "C:\\Users\\<USER>\\Documents\\affaires\\24M053.0 Indice de livrabilité\\lausanne\\SpeedData\\outputFolder\\speed_data_lausanne_logistiscore.csv",
                "--output-network", "C:\\Users\\<USER>\\Documents\\affaires\\24M053.0 Indice de livrabilité\\results",
                "--criteria-types", "C5",
            ]
        }
    ]
}