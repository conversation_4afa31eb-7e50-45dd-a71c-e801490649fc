![TEAM:CLIENTS:0-EN COURS:INTERFACE TRANSPORT:WIP:IDENTITÉ:LOGO:Logo-01.jpg](data:image/jpeg;base64...)

|  |  |  |  |
| --- | --- | --- | --- |
| Note de travail | | | Date : 01.2025 |
| Dossier | 23I714.0 / 24M053 COMO – Indice de livrabilité | | |
| Client |  | | |
| Rédigé par | NBA / CBR | | |
| Validé par |  | | |
| Objet : | | Spécification technique pour le développement de l’outil d’analyse de l’indice de livrabilité V2 | |

Sommaire

[Description 2](#_Toc197942589)

[Versionnement 2](#_Toc197942590)

[Briques fonctionnelles 3](#_Toc197942591)

[Schéma de fonctionnement par produit de sortie 9](#_Toc197942592)

[Détails techniques (suggestions, à compléter) 11](#_Toc197942593)

[Interface utilisateur 11](#_Toc197942594)

# Description

L’Indice de livrabilité permet d’évaluer l’impédance d’un réseau routier pour le transport de marchandises urbain. En d’autres termes, c’est un indice de performance qui permet de quantifier la facilité (ou difficulté) avec laquelle les véhicules de livraison peuvent circuler et effectuer leurs opérations sur le territoire. Il est généré par un outil de calcul qui évalue le réseau en fonction de plusieurs critères, tels que : taux de congestion, type de carrefour, vitesse autorisée, présence d’obstacle, présence des cases de livraison, déclivité, réglementation d’horaire et de gabarit, etc... Les résultats donnent ensuite la possibilité de produire des cartographies mettant en valeur les tronçons selon leur note et montrant ainsi la répartition spatiale de la performance d’un territoire donné au regard du transport de marchandise et en fonction de différente typologie de véhicules de livraison : Poids lourd, véhicules utilitaires légers et vélo-cargo. Ces résultats peuvent ensuite être utilisés dans le cadre d’étude ou de projet, comme outil d’aide à la décision.

C’est un outil à vocation interne, qui sera utilisé par des ingénieurs au sein de l’entreprise.

# Versionnement

Les produits de sortie et fonctionnalité principale de l’outil

### V1 (terminé):

* Calcul approximatif du nombre de mouvement généré par les Points Of Interest
* Diagnostic : Calcul des critères et indice global
* Production cartographique (symbologie) et de tableau de résultats
* Mise en ligne sur ArcGIS Online

### V2 (en cours) :

Cas d’usage (UC)

|  |  |  |
| --- | --- | --- |
| UC1 | Diagnostic de la livrabilité du réseau routier | * Calcul des indicateurs de performance globaux (logistique, environnemental, économique…) :   + Nombre de mouvement   + Indice de livrabilité : Logistiscore + critères |
| UC2 | Caractérisation de la livrabilité du territoire | * Notation de la livrabilité (fluidité, mixité, réglementation, manutention…) des tronçons * Production cartographique intégrable dans les logiciels SIG |
| UC3 | Analyses prospectives | * Évolution de l’impact des politiques publiques sur l’indice de livrabilité |
| UC4 | Évaluation de l’intensité logistique d’un territoire | * Estimation des mouvements générés par les établissements * Production cartographique intégrable dans les logiciels SIG |
| UC5 | Dimensionnement du besoin théorique en cases de livraison |  |

### V3 (en recherche) :

Enquêtes auprès des entreprises suisses pour affiner le calcul de l’intensité logistique

Travail de recherche académique sur les critères et les méthodes de calculs

* Même fonctionnalité que V2
* Analyse demande et accessibilité site foncier identifié
* Analyse d'impact de la construction d'un nouveau quartier (eco-quartier) (mvmts marchandises/flux)

# Briques fonctionnelles

![](data:image/png;base64...)

## Géotraitement SIG

### Calcul du nombre de mouvements :

Principe : À partir d’un tableau de données des établissements, génération d’un nombre de mouvements par établissement selon une règle de calcul définie par IT. Le total des mouvements est calculé par tronçon pour en mesurer l’intensité logistique.

Le nombre de mouvements est attribué à chaque établissement en fonction de : son code d’activité NOGA[[1]](#footnote-2) et de sa taille (par pallier ou en valeur réelle, à confirmer selon la source de données). Les facteurs de génération sont calculés par IT sur la base de résultats générés par le logiciel Silogues.

Soit le nombre de mouvements associés à un tronçon T, le nombre de mouvements associés à un établissement E

### Notation individuelle des tronçons

Les tronçons sont évalués par critère et par gabarit de véhicules (PL, VUL, VC), pour une période temporelle définie. Deux formes de critères sont possibles : quantitatifs (en majorité) ou qualitatifs.

La note attribuée à chaque tronçon pour chaque critère dépend de la valeur qui caractérise ce tronçon, comparée à des valeurs seuils. Par exemple : si la limite de vitesse est de 30km/h et que l’on a le tableau de répartition suivant : ≤10km/h = Note 1 ; ≤20km/h = Note 2 ; >=30km/h = Note 3. Le tronçon en question recevra la note de 3.

La notation des tronçons est caractérisée en fonction de seuils à partir desquels les attributs du tronçon sont jugés comme « bon » ou « mauvais. La note de 1 une performance bonne ou mauvaise des indicateurs. Les bornes sont incluses dans les extrémités des intervalles.

Le détail de la méthode de notation (division en 3 notes).

Le tableau ci-dessous détaille les critères, leur mode de calcul et leur poids dans le calcul du logistiscore. Dans ce tableau, T correspond à un tronçon quelconque.

Tableau 1: Mode de calcul des critères

|  |  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- | --- |
| **Critère** | | | **Mesure** | **Mode de calcul** | **Données source** | **Poids critère** |
| **C1** | ![Une image contenant noir, obscurité  Description générée automatiquement](data:image/png;base64...) | **Voie de circulation** | PL et VUL : Nombre de voie de circulation disponible  Vélo-cargo : Bande cyclable et nombre de voie | Critère quantitatif | Here NAVSTREETS :  Voies de circulation PL/VUL : | A définir |
| **C2** | ![](data:image/x-emf;base64...) | **Transports publics** | Fréquence de passage aux arrêts | Se baser sur une moyenne par jour ? – Heure de pointe du matin. 7h – 11h  Où S est le nombre de seuil  Seuils à définir | GTFS -> Passage dans le préalable dans l’outil de géotraitement ArcGIS (sauf si processus intégré à l’outil et/ou equivalent avec un autre traitement)   * GTFS to Public Transit Data Model * Calculate Transit service Frequency | A définir |
| **C3** | ![Une image contenant noir, obscurité  Description générée automatiquement](data:image/png;base64...) | **Obstacle** | Nombre d’obstacle sur le tronçon | Critère quantitatif  Obstacle :   * Passage piéton * Dos d’âne / Ralentisseur * Passage à niveau (train/tram) | Here NAVSTREETS | A définir |
| **C4** | ![Une image contenant noir, croquis, léger  Description générée automatiquement](data:image/png;base64...) | **Carrefour** | Type de carrefour | Critère qualitatif  Pour tous les tronçons adjacents à un carrefour | Here NAVSTREETS | A définir |
| **C5** | ![Une image contenant noir, obscurité  Description générée automatiquement](data:image/png;base64...) | **Vitesse** | Vitesse moyenne pratiquée sur le tronçon | Critère quantitatif | Here SPEEDATA  Ou  Autre source | A définir |
| **C6** | ![](data:image/x-emf;base64...) | **Congestion** | Indice de calcul de la congestion | Critère quantitatif    Avec :   * = Speed Performance Index pour l’observation (p.ex, un intervalle de 30min) * = Vitesse moyenne observée pour l’observation * = Vitesse en freeflow observée pour l’observation (en général consistante par tronçon) * = Speed Performance Index moyen pour le tronçon * = nombre de jour observé \* nombre d’observation par jour * = Road Segment Congestion Index pour le tronçon * = Temps en état non congestionné () pour le tronçon . * = Temps total d’observation | Here SPEEDATA  Ou  Autre source | A définir |
| **C7** | ![](data:image/x-emf;base64...) | **Chantier** | Impact et durée des chantiers | Critère qualitatif  Chantier ayant un impact sur la voirie. | Here API Incidents  Ou  Base de données locales | A définir |
| **C8** |  | **Gabarit** | Limite de gabarit des véhicules | Critère quantitatif | Here NAVSTREETS | A définir |
| **C9** | ![](data:image/x-emf;base64...) | **Horaire** | Nombre d’heures autorisées aux livraisons | Critère quantitatif | Here NAVSTREETS | A définir |
| **C10** | ![Une image contenant noir, obscurité  Description générée automatiquement](data:image/png;base64...) | **Case de livraison** | Nombre de cases de livraison à proximité | Critère quantitatif  À préciser avec le statut d’aire piétonne du tronçon considéré | Base locale | A définir |
| **C11** | ![Une image contenant noir, obscurité  Description générée automatiquement](data:image/png;base64...) | **Déclivité** | Pente maximum du tronçon | Critère quantitatif | Here API ADAS | A définir |

Le tableau ci-dessous explicite les seuils à partir desquels l’indicateur est jugé comme « bon » ou « mauvais ». Note de lecture :

* BORNE\_M : « Borne mauvaise » correspond à la valeur maximale de la notation 1
* BORNE\_B : « Borne bonne » correspond à la valeur minimale de la notation 3

Tableau 2: Seuil de définition des notations

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| **ID** | **VEHICULE** | **PARAM** | **BORNE\_B** | **BORNE\_M** | **UNITE** |
| C1 | PL | Nombre de voie de circulation | 3 | 1 |  |
|  | VUL |  | 2 | 1 |  |
|  | VC | Présence de bande cyclable et nombre de voie | 2 | 1 |  |
| C2 | PL | Nombre de passage moyen par heure | 5 | 15 | À revoir |
|  | VUL |  | 15 | 45 |  |
|  | VC |  | 30 | 60 |  |
| C3 | PL | Nombre d’obstacle par tronçons | 0 | 3 | Obstacle/mètre |
|  | VUL |  | 0 | 4 | - |
|  | VC |  | 4 | 5 | - |
| C4 | PL | Catégorie de carrefour | - | - | - |
|  | VUL |  | - | - | - |
|  | VC |  | - | - | - |
| C5 | PL | Vitesse moyenne pratiqué sur le tronçon en km/h | 25 | 15 | Km/h |
|  | VUL |  | 35 | 20 | Km/h |
|  | VC |  | 15 | 10 | km/h |
| C6 | PL | Valeur de l’indice RI | 0,7 | 0,3 | - |
|  | VUL |  | 0,8 | 0,4 | - |
|  | VC |  | 0,6 | 0,2 | - |
| C7 | PL | Présence de chantier | - | - | - |
|  | VUL |  | - | - | - |
|  | VC |  | - | - | - |
| C8.1 | PL | Hauteur limite autorisée | 370 | 330 | cm |
|  | VUL |  | 280 | 205 | cm |
|  | VC |  | 200 | 180 | cm |
| C8.2 | PL | Poids limite autorisé | 19000 | 10000 | kg |
|  | VUL |  | 3500 | 2500 | kg |
|  | VC |  | 0 | 0 | kg |
| C8.3 | PL | Longueur limite autorisée | 11 | 9 | m |
|  | VUL |  | 6,5 | 5 | m |
|  | VC |  | 0 | 0 | m |
| C8.4 | PL | Largeur limite autorisée | 250 | 210 | cm |
|  | VUL |  | 210 | 189 | cm |
|  | VC |  | 120 | 99 | cm |
| C9 | PL | Durée autorisée à la circulation des véhicules de livraison | 8 | 4 | h |
|  | VUL |  | 8 | 4 | h |
|  | VC |  | 0 | 0 | h |
| C10 | PL | Nombre d'aire de livraison | 2 | 0 | Nombre d'AL / 100m |
|  | VUL |  | 2 | 0 | - |
|  | VC |  | 0 | 0 | - |
| C11 | PL | Pente maximale du tronçon | 3 | 5 | deg |
|  | VUL |  | 6 | 10 | deg |
|  | VC |  | 3 | 6 | deg |

#### Critère 1 : Voies de circulation

La définition de ce critère est dépendante du véhicule choisi. Pour les PL et VUL, c’est le nombre de voie de circulation disponible qui est considéré ; et pour le vélo-cargo, la présence de bande cyclable est également prise en compte.

Les voies de circulation sont définies à partir des attributs LANE\_CAT, DIR\_TRAVEL et LANE\_TYPE de la couche Streets du réseau NAVSTREETS :

Tableau 3: Attributs nécessaires au calcul du critère C1

|  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- |
| Couche | Attribut | Type | Description | PL/VUL | Vélo Cargo |
| Streets | LANE\_CAT | Integer | Catégorisation en fonction du nombre de voie par sens :   * 1 = max 1voie/sens * 2 = max 2 voies/sens * 3 = + de 2 voies /sens | **X** | **X** |
| Streets | DIR\_TRAVEL | String | Direction de circulation   * F= From * T = To * B = Both | **X** |  |
| Lane | LANE TYPE | Integer | Type de voie spéciale (65536 = bande cyclable) |  | **X** |

Le nombre de voies est estimé en fonction du gabarit du véhicule :

* Pour les PL et VUL,

Tableau 4: (RG1) Nombre de voies pour les PL et VUL

![Une image contenant texte, capture d’écran, Police, ligne

Le contenu généré par l’IA peut être incorrect.](data:image/png;base64...)

Si le nombre de voie total est supérieur ou égal au « seuil bon » définit dans le Tableau 2: Seuil de définition des notations, la performance est notée « Bon » (= 3). Si ce nombre est inférieur ou égal au « seuil mauvais », la note est « Mauvais » (= 1). Entre deux, la note est « Moyen » (= 2).

* Pour les vélos cargo,
  + LANE\_CAT <= 1 : Note = 3
  + LANE\_CAT <= Borne « mauvaise » (cf. Tableau 2) et LANE\_TYPE=65536 : Note =2
  + Autrement, Note = 1

Pseudo-code :

SI

* Nombre de ligne physique est supérieur ou égale au seuil bon OU
* Nombre de voie dans la direction “From” plus le nombre de voir dans la direction “To” est supérieur ou égale au seuil bon OU
* catégorie de voies est strictement supérieure au seuil bon OU
* catégorie de voies est supérieur ou égale au seuil bon ET la direction de circulation est dans les deux sens) :
* La note est de 3

SINON SI

* Nombre de ligne physique est supérieur au seuil mauvais OU
* Nombre de voie dans la direction “From” plus le nombre de voir dans la direction “To” est supérieur au seuil mauvais OU
* catégorie de voies est strictement supérieure au seuil mauvais ET la direction de circulation est en sens unique) OU
* catégorie de voies est supérieur ou égale au seuil mauvais ET la direction de circulation est dans les deux sens) :
* La note est de 2

SINON SI

* Nombre de voie dans la direction “From” plus le nombre de voir dans la direction “To” est inférieur ou égale au seuil mauvais OU
* catégorie de voies est strictement inférieure au seuil mauvais OU
* catégorie de voies est inférieur ou égale au seuil mauvais ET la direction de circulation est en sens unique) :
* La note est de 1

SINON :

* Gestion de l’erreur

#### Critère 2 :

WIP

#### Critère 3 :

WIP

#### Critère 4 :

WIP

#### Critère 5 : Vitesse moyenne pratiquée sur le tronçon

Ce critère est calculé à partir des données FCD extraite de HERE et notamment de la vitesse moyenne observée sur le tronçon. La notation est définie par gabarit de véhicules.

Pour un gabarit de type poids lourds,

* Vitesse moyenne <15 km/h : Note C5 = 1
* Vitesse moyenne comprise entre 15 et 25 km/h : Note C5 = 2
* Vitesse moyenne >= 25km/h : Note C5 = 3

Pour un gabarit de type VUL,

* Vitesse moyenne <20 km/h : Note C5 = 1
* Vitesse moyenne comprise entre 20 et 35 km/h : Note C5 = 2
* Vitesse moyenne >= 35km/h : Note C5 = 3

Pour un gabarit de type VC,

* Vitesse moyenne <10 km/h : Note C5 = 1
* Vitesse moyenne comprise entre 10 et 15 km/h : Note C5 = 2
* Vitesse moyenne >= 15km/h : Note C5 = 3

#### Critère 6 :

WIP

#### Critère 7 :

WIP

#### Critère 8 :

WIP

#### Critère 9 :

WIP

#### Critère 10 :

WIP

La donnée d’entrée permettant le calcul de ce critère est dépendante du territoire qui l’a fourni. En ce sens, les filtres pour identifier les cases de livraisons diffèrent d’un territoire à l’autre.

###### Sur le territoire de Gèneve

Les cases de livraisons sont identifiables avec le filtre : *TYPE\_STAT = "Gratuit jaune"*

###### Sur le Canton de Vaud

Les cases de livraisons sont identifiables avec le filtre : *type\_txt = "Livraisons"*

#### Critère 11 Déclivité:

Note C11(T)= 〖abs(pente〗_T) ⇔ 〖seuils〗_C11
La notation est définie par gabarit de véhicules.
Pour un gabarit de type poids lourds,
	〖abs(pente〗_T) < 3% : Note C11 = 1
	〖abs(pente〗_T)  compris entre 3 et 5% : Note C11 = 2
	〖abs(pente〗_T) >= 5% : Note C11 = 3
Pour un gabarit de type VUL,
	〖abs(pente〗_T) < 6% : Note C11 = 1
	〖abs(pente〗_T)  compris entre 6 et 10% : Note C11 = 2
	〖abs(pente〗_T) >= 10% : Note C11 = 3
Pour un gabarit de type VC,
	〖abs(pente〗_T) < 3% : Note C11 = 1
	〖abs(pente〗_T)  compris entre 3 et 6% : Note C11 = 2
	〖abs(pente〗_T) >= 6% : Note C11 = 3

### Logistiscore d’un tronçon

avec :

Les notes par tronçon sont ensuite normalisées (rapportées à une valeur entre 0 et 100) pour évaluer le logistiscore du tronçon\*.

Intensité logistique par tronçons

avec :

Densité linéaire de l’intensité logistique

avec :

Densité normalisée

avec :

\*Pour le calcul et la cartographie générale, il faut trouver un moyen de générer un Logistiscore par tronçon qui intègre le nombre de mouvement sur chaque tronçon. En soi : superposer la couche de notation de critères avec celle du nombre de mouvement (intensité logistique)

### Notation territoire : le Logistiscore

Qualification du réseau par critère

Le réseau d’un territoire est qualifié au niveau de chaque critère permettant d’identifier ceux dans lesquels le réseau performe ou, au contraire, ceux qui sont problématiques.

Pour chaque critère, la note du réseau st estimée à l’aide d’une moyenne pondérée des notes de chaque tronçon, du nombre de mouvement et la longueur :

avec :

Ces notes par critère sont ensuite normalisées pour être rapportée à une valeur entre 0 et 100 afin d’être plus lisibles et indépendantes du système de notation préalablement choisi.

Qualification du réseau global

avec :

Exemple synthétique du calcul :

|  |  |  |  |  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| Tronçon | Longueur | Mouvement | Note C1 | Note C2 | Note C3 | Note C4 | Note C5 | Moyenne Tronçon | Moyenne normalisée/100 |
| ID1 | 22 | 18 | 1 | 3 | 2 | 3 | 2 | 2,2 | 60 |
| ID2 | 107 | 25 | 2 | 1 | 2 | 3 | 3 | 2,2 | 60 |
| ID3 | 395 | 5 | 2 | 1 | 2 | 2 | 1 | 1,6 | 30 |
| ID4 | 58 | 37 | 3 | 2 | 3 | 3 | 1 | 2,4 | 70 |

|  |  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- | --- |
| Total | Note C1 | Note C2 | Note C3 | Note C4 | Note C5 | Moyenne globale/100 = *Logistiscore* |
| Moyenne pondérée | 2,24332592 | 1,40850945 | 2,2983871 | 2,72538932 | 1,79894327 |  |
| Moyenne normalisée | 62 | 20 | 65 | 86 | 40 | 55 |

|  |  |
| --- | --- |
| Critère | Poids |
| C1 | 1 |
| C2 | 1 |
| C3 | 1 |
| C4 | 1 |
| C5 | 1 |

## Compatibilité des résultats

Le résultat du Logistiscore doit être retourné dans un fichier compatible SIG (csv avec Well-Known Text, .shp, .geojson, …) pouvant être ouvert dans un logiciel SIG et représentant le réseau routier, avec le détail des notes de critère en attribut.

La couche de données contenant les attributs doit intégrer les éléments suivants :

* Le Logistiscore du territoire
* Pour chaque tronçon :
  + Id
  + Nom
  + Longueur
  + Intensité logistique (nombre de mouvements)
  + Note pour chaque critère (11 en tout)
  + Indice de livrabilité du tronçon

### Format de données

Contenu du fichier de sortie à détailler

## Analyse et publication

Production de documents (pdf, svg, xlsx) avec plusieurs produits de sortie de l’outil : indicateurs, graphiques et evt extrait de cartographie « standards », qui pourront être intégrés dans un livrable.

### Indicateurs

* Population / Emploi / …
* Longueur du réseau routier, surface du territoire
* Nombre de mouvements totaux / par emploi / par habitant / par m2 / …
* Note globale
* Pourcentage de tronçons avec une note inférieur à la moyenne
* …

### Cartographie

* Carte des mouvements (carte de chaleurs)
* Carte de résultat de diagnostic (carte du réseau avec symbologie appropriée)

(Stratégie à déterminer plus précisément pour appliquer une symbologie automatiquement : fichier « layer » ? fonction de code ?)

### Graphiques

* Histogrammes (Notes par critères)
* Camembert (établissement / mouvements par NOGA, notes par typologie de tronçons, …)
* Nuages de pointsDonnées d’entrée

# Données d’entrée

(cf [document Note Chiffrage.docx](https://citecch.sharepoint.com/%3Aw%3A/s/INTERFACE_TRANSPORT/Ea7BHpZmpedPlZERsBa-cAABip40S3kKNuug3zoyCMGMaw) )

|  |  |  |  |  |
| --- | --- | --- | --- | --- |
| Source | Étendue | Format | Utilisation | Localisation |
| NOGA | Suisse | Excel | Nomenclature des établissements suisses. Utilisé pour le calcul de l’intensité logistique | Nomenclature 2025 : [NOGA2025\_fr.xlsx](https://citecch.sharepoint.com/%3Ax%3A/s/BIBLIO/EW9WDKMkCrROtzbw8m6nPMcBNyEKIzeqUNyJEJI_FJV4rg?e=ZJthgw&xsdata=MDV8MDJ8fDc3MTI3Zjk2YWUzNTQ5MTU0N2YyMDhkZDhkNGE5MTdlfGY1MGVjODZkNzM1ZDRkNWJiZDFjMTdiNzhjZDAwZTRifDB8MHw2Mzg4MjIwNzM1NjQ1NDcwNTh8VW5rbm93bnxWR1ZoYlhOVFpXTjFjbWwwZVZObGNuWnBZMlY4ZXlKV0lqb2lNQzR3TGpBd01EQWlMQ0pRSWpvaVYybHVNeklpTENKQlRpSTZJazkwYUdWeUlpd2lWMVFpT2pFeGZRPT18MXxMMk5vWVhSekx6RTVPakF4WWpjMllUVXpMVGxpTlRrdE5EVmpZeTFoWVRVekxUYzNaREl5TVRjd09HTXpZMTltWkRoa09UaGtNUzB4TlRVd0xUUXdNVE10WVRGbVppMHlNbVZpT0RObFkyVTVZemxBZFc1eExtZGliQzV6Y0dGalpYTXZiV1Z6YzJGblpYTXZNVGMwTmpZeE1EVTFOVEF6TkE9PXwyODI1OTg0MjI2NGM0YmFjNDdmMjA4ZGQ4ZDRhOTE3ZXw5OTg4NzUzZjE0NzU0NTRhYmY2NTI2MzlmMWRhNjEyOA%3D%3D&sdata=V0E5bFFDTnZzenNWNk1RSXV4dW5UZWVqOHRmQTc4elM5bENEd212VklMWT0%3D&ovuser=f50ec86d-735d-4d5b-bd1c-17b78cd00e4b%2Ccamille.brisson%40modelity.net)  Tableau de correspondance NOGA 2008 et 2025 :  [NOGA\_Table\_correspondances\_2008\_2025.xlsx](https://citecch.sharepoint.com/%3Ax%3A/s/BIBLIO/EVdTyK211zVKvKLtsiUsePcBMXoiV03375HO7xDUWirSXg?e=g09C8U&isSPOFile=1&xsdata=MDV8MDJ8fDAxODE1ZjdlYTkxMzQxMjJlNzM4MDhkZDkxMzIxOTkyfGY1MGVjODZkNzM1ZDRkNWJiZDFjMTdiNzhjZDAwZTRifDB8MHw2Mzg4MjYzNjY1MTk5Mjg2MzJ8VW5rbm93bnxWR1ZoYlhOVFpXTjFjbWwwZVZObGNuWnBZMlY4ZXlKV0lqb2lNQzR3TGpBd01EQWlMQ0pRSWpvaVYybHVNeklpTENKQlRpSTZJazkwYUdWeUlpd2lWMVFpT2pFeGZRPT18MXxMMk5vWVhSekx6RTVPakF4WWpjMllUVXpMVGxpTlRrdE5EVmpZeTFoWVRVekxUYzNaREl5TVRjd09HTXpZMTltWkRoa09UaGtNUzB4TlRVd0xUUXdNVE10WVRGbVppMHlNbVZpT0RObFkyVTVZemxBZFc1eExtZGliQzV6Y0dGalpYTXZiV1Z6YzJGblpYTXZNVGMwTnpBek9UZzFNVEF5TlE9PXxlMDQxN2I5MDVkYWI0MGI5ZTczODA4ZGQ5MTMyMTk5MnxjYmE3Yjk2Y2M4YTQ0Zjk3OTFiYjc2M2Y5YTE2NzUxMg%3D%3D&sdata=UUZHa2grRXMvVVcxMzU1L240RENBMmp0YU44N0gwaWdwbU8rSG9qRnZmUT0%3D&ovuser=f50ec86d-735d-4d5b-bd1c-17b78cd00e4b%2Ccamille.brisson%40modelity.net)  (nomenclature 2008 n’est plus à disposition) |
| STATENT | Suisse | CSV | Evaluer l’intensité logistique d’un tronçon : recensement des établissements et emplois. | Dernière version basé sur la nomenclature NOGA 2008: [DSV 250108 STATENT LOC 2022.xlsx](https://citecch.sharepoint.com/%3Ax%3A/s/INTERFACE_TRANSPORT/EQkAeGQ_f1BKvuf0t5DUvtsBjazJeVZGZ3Uw7Dwse2nqCQ?e=pzhoOb&xsdata=MDV8MDJ8fDc3MTI3Zjk2YWUzNTQ5MTU0N2YyMDhkZDhkNGE5MTdlfGY1MGVjODZkNzM1ZDRkNWJiZDFjMTdiNzhjZDAwZTRifDB8MHw2Mzg4MjIwNzM1NjQ1NDcwNTh8VW5rbm93bnxWR1ZoYlhOVFpXTjFjbWwwZVZObGNuWnBZMlY4ZXlKV0lqb2lNQzR3TGpBd01EQWlMQ0pRSWpvaVYybHVNeklpTENKQlRpSTZJazkwYUdWeUlpd2lWMVFpT2pFeGZRPT18MXxMMk5vWVhSekx6RTVPakF4WWpjMllUVXpMVGxpTlRrdE5EVmpZeTFoWVRVekxUYzNaREl5TVRjd09HTXpZMTltWkRoa09UaGtNUzB4TlRVd0xUUXdNVE10WVRGbVppMHlNbVZpT0RObFkyVTVZemxBZFc1eExtZGliQzV6Y0dGalpYTXZiV1Z6YzJGblpYTXZNVGMwTmpZeE1EVTFOVEF6TkE9PXwyODI1OTg0MjI2NGM0YmFjNDdmMjA4ZGQ4ZDRhOTE3ZXw5OTg4NzUzZjE0NzU0NTRhYmY2NTI2MzlmMWRhNjEyOA%3D%3D&sdata=WUtPQkhYYWVXL3ZrSEhjZ01mSFpZcS9jSi9lY0VaZmtMT1g0UnRBaUtlVT0%3D&ovuser=f50ec86d-735d-4d5b-bd1c-17b78cd00e4b%2Ccamille.brisson%40modelity.net)  Détails des colonnes : [do-b-06-STATENT-VL-01(1).xlsx](https://citecch.sharepoint.com/%3Ax%3A/s/INTERFACE_TRANSPORT/EdHS2Esgx9FBt7uOV0qg6o4BUeul9KF_K33OZNx0ChOq_w?e=5Tb3db&isSPOFile=1&xsdata=MDV8MDJ8fDAxODE1ZjdlYTkxMzQxMjJlNzM4MDhkZDkxMzIxOTkyfGY1MGVjODZkNzM1ZDRkNWJiZDFjMTdiNzhjZDAwZTRifDB8MHw2Mzg4MjYzNjY1MTk5Mjg2MzJ8VW5rbm93bnxWR1ZoYlhOVFpXTjFjbWwwZVZObGNuWnBZMlY4ZXlKV0lqb2lNQzR3TGpBd01EQWlMQ0pRSWpvaVYybHVNeklpTENKQlRpSTZJazkwYUdWeUlpd2lWMVFpT2pFeGZRPT18MXxMMk5vWVhSekx6RTVPakF4WWpjMllUVXpMVGxpTlRrdE5EVmpZeTFoWVRVekxUYzNaREl5TVRjd09HTXpZMTltWkRoa09UaGtNUzB4TlRVd0xUUXdNVE10WVRGbVppMHlNbVZpT0RObFkyVTVZemxBZFc1eExtZGliQzV6Y0dGalpYTXZiV1Z6YzJGblpYTXZNVGMwTnpBek9UZzFNVEF5TlE9PXxlMDQxN2I5MDVkYWI0MGI5ZTczODA4ZGQ5MTMyMTk5MnxjYmE3Yjk2Y2M4YTQ0Zjk3OTFiYjc2M2Y5YTE2NzUxMg%3D%3D&sdata=akhoSjRwZ0ZkZllFZWZpb0VFZWVETys2ZXBKai8yNjJmK3pBRVEyMm1IST0%3D&ovuser=f50ec86d-735d-4d5b-bd1c-17b78cd00e4b%2Ccamille.brisson%40modelity.net) |
| NAVSTREETS™   * *Streets* * *Cdms* * *CdmsDtmod* * *CndMod* * *Lane* * *(Point of Interest)* * *(Adminbndy4)* | Territoire d’étude | Shapefile ou géodonnées | Modélisation du réseau routier ;  Calcul d’indicateurs (Nombre de voies) |  |
| ADAS | Territoire d’étude | Requête API[[2]](#footnote-3) | Calcul de la pente des tronçons |  |
| Speed Data (Floating car data) | Territoire d’étude | Requête API ou via plateforme HERE Traffic Analytics,   * Renvoi un fichier .csv | * Calcul d’un indicateur de congestion par tronçons d’après les vitesses commerciales * Vitesse moyenne observée |  |
| Incidents | Territoire d’étude | Requête API[[3]](#footnote-4) | Calcul d’un indicateur d’impact chantier |  |
| Géoportails locaux | Territoire d’étude |  | Données complémentaires sur le réseau  Couche des cases de livraison | **Genève  - case de livraison** :  [SHP\_OTC\_STATIONNEMENT\_V\_PUBLIQUE](https://citecch.sharepoint.com/%3Af%3A/s/BIBLIO/Eh4nv7jJIfBJtH1-HsI64koBS4HvpQcc5Vp7mOGGcZ3m0g?e=GKu9lt) Filtre à appliquer : *TYPE\_STAT = "Gratuit jaune"*  **Canton de Vaud : case de livraison :**  [Stationnement](https://citecch.sharepoint.com/%3Af%3A/s/BIBLIO/EmRhRBIjIklHlHNXaEVwZ64BFnHngFidFuvOxmulC2qORw?e=GIgWcS) – tous les fichiers nommés « stationnement\_parking » avec le filtre type\_txt = "Livraisons" |

## Validité et cohérence des sources de données

La dernière version disponible des données STATENT date de 2022, elle est basée sur la nomenclature de 2008. En 2025, la nomenclature NOGA a été revue intégralement donnant lieu à la création de la nomenclature NOGA 2025. La version 2008 est devenue inaccessible. Une table de correspondance NOGA 2008 – 2025 a été créée pour assurer la cohérence entre les données STATENT 2022 et la nomenclature NOGA actuelle. Dès lors, l’identification du code NOGA dans les données STATENT 2022 se fait grâce à la table de correspondance NOGA 2008 – 2025.

## Vérification / Contrôle

IT : Sur l’aspect métier/logistique (cohérence avec la réalité)

Modelity : Sur aspect data analysis

# Schéma de fonctionnement par produit de sortie

![](data:image/png;base64...)

### UC1 : Diagnostic de la livrabilité du territoire

Entrée :

* Réseau NAVSTREETS HERE
* Donnée complémentaires HERE
* SpeedData TomTom
* Données complémentaires OFS / Géoportail / OpenData
* STATENT

Fonctionnement :

1. Traitement des données
2. Calcul du nombre de mouvements
3. Évaluation et notation des critères
4. Calcul des indicateurs de performance globaux
5. Cartographie
6. Production du rapport

Sortie :

* Rapport de synthèse
* Tableau des résultats/indicateurs, rapport synthétique pdf, fichier pour lecture SIG

### UC2 : Caractérisation de la livrabilité des territoires

Entrée :

* Réseau NAVSTREETS HERE
* Donnée complémentaires HERE
* SpeedData TomTom
* Données complémentaires OFS / Géoportail / OpenData
* STATENT

Fonctionnement :

1. Traitement des données
2. Calcul du nombre de mouvements
3. Évaluation et notation des critères
4. Calcul des indicateurs de performance globaux

Sortie : Tableau des résultats/indicateurs, rapport synthétique pdf

### UC3 : Analyses prospectives

Entrée :

* Réseau NAVSTREETS HERE
* Donnée complémentaires HERE
* SpeedData TomTom
* Données complémentaires OFS / Géoportail / OpenData
* STATENT

Fonctionnement :

1. Traitement des données
2. Modification des données (changement de paramètres ou d’attributs selon la situation projetée)
3. Calcul du nombre de mouvements
4. Évaluation et notation des critères
5. Calcul des indicateurs de performance globaux

Sortie : Tableau des résultats/indicateurs, rapport synthétique pdf, fichier pour lecture SIG

### UC4 : Évaluation de l’intensité logistique d’un territoire (rue, quartier, ville…)

Entrée :

1. Réseau NAVSTREETS HERE
2. STATENT

Fonctionnement :

1. Traitement des données
2. Calcul du nombre de mouvements

Sortie : fichier pour lecture SIG

### UC 5 : Dimensionnement du besoin théorique en cases de livraison sur un secteur

Entrée :

1. Réseau NAVSTREETS HERE
2. STATENT

Fonctionnement :

1. Traitement des données
2. Calcul du nombre de mouvements
3. Calcul du nombre de cases de livraison

Sortie : Tableau des résultats/indicateurs, rapport synthétique pdf, fichier pour lecture SIG

# Détails techniques (suggestions, à compléter)

* L’outil de calcul du nombre de mouvements doit être séparé de l’outil de calcul de la livrabilité, pour permettre son utilisation indépendante.
* Paramètres de l’outil dans un fichier externe pour permettre de modifier les facilement les valeurs d’entrées, notamment dans la phase de développement (ex. : Prise en compte de certains critères ou non, Pondération des critères, Facteurs de génération de mouvements, seuils ou bornes déterminantes pour la notation, …).
* Calcul des critères par bloc de code : un critère = une fonction, pour permettre plus de flexibilité.

# Interface utilisateur

L’interface utilisateur sera développée avec Experience Builder d’ArcGIS. ArcGIS Experience Builder est un outil de développement d’interfaces web interactives qui permet de créer des applications web cartographiques personnalisées.

Pour des questions de performance, l’interface utilisateur sera décorrélée de l’outil de calcul. Ce dernier sera exécuté en amont et ses résultats seront récupérés par l’interface pour être visualisé par l’utilisateur.

## Version 1

### Description

La V1 de l’interface utilisateur sert à démontrer l’intérêt et l’apport d’une application web dans le projet. Cette version a pour objectif de visualiser l’application finale et adapter ensuite le visuel dans les itérations suivantes. L’indicateur qui sera affiché est un indice de livrabilité factice permettant de s’abstreindre des délais de conception et de développement de l’indice.

### Fonctionnalités

##### Sélection du Territoire

L’utilisateur peut choisir un territoire (ex. : ville, département, région) parmi un ensemble de territoires disponibles. Dans le cadre de la version 2 du projet, deux territoires seront disponibles : Genève et Lausanne. Un menu à choix multiples déroulant les territoires disponibles permettra de faire la sélection.

Une fois le territoire choisi, l’application charge le réseau routier correspondant.

##### Chargement du réseau routier

Le réseau routier est récupéré via la source de données externe définie dans la section Données d’entrée. Les tronçons routiers sont affichés sur la carte avec une stylisation de base.

1. **Affichage de l’Indicateur**

Chaque tronçon routier est caractérisé par un ensemble d’attributs qui entrent dans le calcul de l’indice de livrabilité. La valeur de l’indicateur est représentée par une jauge de couleur selon une échelle à définir. Une légende est disponible pour comprendre la signification des couleurs.

En cliquant sur un tronçon, les informations détaillées associées s’affichent dans le bandeau vertical présent à droite de la carte (voir section 4 – Interface Utilisateur). Les attributs à afficher sont à définir mais il est possible d’ores et déjà de lister les principaux : l’indice de livrabilité individuel et tous les critères utilisés pour le calculer porteur d’intérêt pour l’utilisateur : vitesse moyenne, vitesse légale, longueur, etc..

Dans le cas où l’indicateur a une dépendance temporelle (ex : sa valeur évolue en fonction de l’heure de la journée), une frise chronologique sera ajoutée en bas de la carte permettant à l’utilisateur de choisir la période qu’il souhaite visualiser.

1. **Interface Utilisateur**

### Version 1

Le territoire présenté est écrit en haut de l’interface.

La carte est située sur le côté gauche de l’interface. Cette carte est interactive, il est permis à l’utilisateur de zoomer et de se déplacer sur le territoire. L’échelle est précisée en bas à droite de la carte en dessous des points cardinaux.

Des widgets de filtres s’ajoutent à la carte pour afficher uniquement certains tronçons. Les filtres sont à définir en fonction de leur pertinence pour l’utilisateur.

A droite de l’interface un bandeau vertical affiche la valeur de l’indice de livrabilité du réseau accompagnée d’indicateurs statistiques agrégés associés. Ces statistiques sont à définir.

Lors que l’utilisateur clique sur un tronçon les attributs, critères qui lui sont associés s’affichent dans la section en bas à droite.

![](data:image/png;base64...)

Représentation simplifiée de la structure de l'interface utilisateur

L’interface utilisateur sera livrée de façon itérative, la version Beta est réaalisée uniquement sur le territoire de Genève, avec l’affichage du critère C5, le logistiscore partiel calculé à partir du critère C5. Schématiquement, l’interface aura l’aspect suivant :

![](data:image/png;base64...)Cette version sera livrée en juin 2025.

### Versions ultérieures

Le territoire est découpé en quartier pré-défini, lorsque l’utilisateur sélectionne un quartier, le logistiscore et les statistiques associées se mettent à jour. Evolution suivante : les indicateurs (logistiscore + statistiques) s’actualisent en fonction du zoom de la carte choisie par l’utilisateur. Le territoire concerné est alors celui qui est affiché sur la carte.

MAJ des indicateurs en fonction du zoom utilisateur : liée à la fenêtre de visualisation.

Comparaison de quartiers : sélection de quartier pour ajout dans le tableau de bord.

1. **Authentification**

L’outil est développé pour un usage interne Citec à destination d’interface Transport. Pour accéder à l’application, l’utilisateur (Interface Transport) doit disposer d’une licence ArcGIS viewer dédiée (208CHF/an en 2025). L’ouverture de l’outil en externe nécessite l’obtention d’une licence ArcGIS par l’utilisateur final.

1. Nomenclature des activités économique <https://www.bfs.admin.ch/bfs/fr/home/<USER>/industrie-services/nomenclatures/noga.html>. Similaire au code NAF en France. [↑](#footnote-ref-2)
2. url : <https://smap.hereapi.com/v8/maps/attributes> [↑](#footnote-ref-3)
3. url : <https://data.traffic.hereapi.com/v7/incidents> [↑](#footnote-ref-4)