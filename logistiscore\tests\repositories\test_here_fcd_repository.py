from datetime import datetime

import pytest

from logistiscore.ir.epoch import <PERSON>poch<PERSON><PERSON><PERSON>, EpochType
from logistiscore.ir.speed import AveragedSpeedData
from logistiscore.repositories.here_fcd_repository import HEREFCDRepository


class TestHEREFCDRepository:
    def test_init_loads_speed_data(self, here_fcd_file: str) -> None:
        repo = HEREFCDRepository(here_fcd_file)

        assert repo.epoch_type == EpochType.EPOCH_60MIN
        assert len(repo.df) == 4
        expected_columns = ["segment_id", "epoch", "speed", "count", "freeflow", "date"]
        assert all(col in repo.df.columns for col in expected_columns)

    def test_get_averaged_speed_data_no_filters(self, here_fcd_file: str) -> None:
        repo = HEREFCDRepository(here_fcd_file)

        result = repo.get_averaged_speed_data()

        assert isinstance(result, AveragedSpeedData)
        assert len(result.measurements) == 4
        assert result.epoch_type == EpochType.EPOCH_60MIN

    def test_get_averaged_speed_data_with_provided_segments_should_filter(
        self, here_fcd_file: str
    ) -> None:
        repo = HEREFCDRepository(here_fcd_file)

        result = repo.get_averaged_speed_data(segment_ids=[10000])

        assert isinstance(result, AveragedSpeedData)
        assert len(result.measurements) == 2
        assert all(m.segment_id == 10000 for m in result.measurements)

    def test_get_averaged_speed_data_with_start_date_should_filter(
        self, here_fcd_file: str
    ) -> None:
        repo = HEREFCDRepository(here_fcd_file)
        start_date = datetime(2025, 5, 16, 8, 30)

        result = repo.get_averaged_speed_data(start_date=start_date)

        assert isinstance(result, AveragedSpeedData)
        assert len(result.measurements) == 2
        assert all(m.epoch in [7, 9] for m in result.measurements)

    def test_get_averaged_speed_data_with_end_date(self, here_fcd_file: str) -> None:
        repo = HEREFCDRepository(here_fcd_file)
        end_date = datetime(2025, 5, 16, 8, 30)
        result = repo.get_averaged_speed_data(end_date=end_date)

        assert isinstance(result, AveragedSpeedData)
        assert len(result.measurements) == 2
        assert all(m.epoch in [6, 8] for m in result.measurements)

    def test_get_averaged_speed_data_with_date_range_should_filter(
        self, here_fcd_file: str
    ) -> None:
        repo = HEREFCDRepository(here_fcd_file)
        start_date = datetime(2025, 5, 16, 8, 30)
        end_date = datetime(2025, 5, 16, 9, 30)

        result = repo.get_averaged_speed_data(start_date=start_date, end_date=end_date)

        assert isinstance(result, AveragedSpeedData)
        assert len(result.measurements) == 2
        assert all(m.epoch in [7, 9] for m in result.measurements)

    def test_get_averaged_speed_data_with_epoch_interval_should_filter(
        self, here_fcd_file: str
    ) -> None:
        repo = HEREFCDRepository(here_fcd_file)
        epoch_interval = EpochInterval(start_epoch=7, end_epoch=9)

        result = repo.get_averaged_speed_data(epoch_interval=epoch_interval)

        assert isinstance(result, AveragedSpeedData)
        assert len(result.measurements) == 3
        assert all(7 <= m.epoch <= 9 for m in result.measurements)

    def test_determine_epoch_type_not_found(self, tmp_path) -> None:
        csv_content = (
            "LINK-DIR,MEAN,COUNT,FREEFLOW,DATE-TIME,EPOCH-UNKNOWN\n"
            "10000F,35.5,10,45.0,2025-05-16 08:00,6\n"
        )
        fcd_file = tmp_path / "test_fcd.csv"
        fcd_file.write_text(csv_content)

        with pytest.raises(ValueError, match="No epoch column found in the data"):
            HEREFCDRepository(str(fcd_file))

    @pytest.mark.parametrize(
        "epoch_column,expected_type",
        [
            ("EPOCH-15MIN", EpochType.EPOCH_15MIN),
            ("EPOCH-5MIN", EpochType.EPOCH_5MIN),
            ("EPOCH-60MIN", EpochType.EPOCH_60MIN),
        ],
    )
    def test__determine_epoch_type_should_return_correct_epoch_type(
        self, tmp_path, epoch_column, expected_type
    ) -> None:
        csv_content = (
            f"LINK-DIR,MEAN,COUNT,FREEFLOW,DATE-TIME,{epoch_column}\n"
            f"10000F,35.5,10,45.0,2025-05-16 08:00,6\n"
        )
        fcd_file = tmp_path / "test_fcd.csv"
        fcd_file.write_text(csv_content)

        repo = HEREFCDRepository(str(fcd_file))

        assert repo.epoch_type == expected_type
        assert repo._determine_epoch_type(str(fcd_file)) == expected_type
