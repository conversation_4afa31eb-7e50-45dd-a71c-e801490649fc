from typing import Dict, List

import pandas as pd

from logistiscore.calculators.criteria_calculator import CriteriaCalculatorFactory
from logistiscore.ir.criteria import CriteriaType, Threshold
from logistiscore.ir.network import (
    Network,
    SegmentScore,
    TerritoryCriteriaScore,
    TerritoryScore,
)
from logistiscore.ir.territory import TerritoryData
from logistiscore.ir.vehicle import VehicleType


class NetworkScoreCalculator:
    def __init__(
        self,
        strategy_factory: CriteriaCalculatorFactory,
        thresholds_config: Dict[CriteriaType, Dict[VehicleType, Threshold]],
    ):
        self.strategy_factory = strategy_factory
        self.thresholds_config = thresholds_config

    def calculate_network(
        self,
        territory_data: TerritoryData,
        criteria_types: List[CriteriaType],
        criteria_weights: Dict[CriteriaType, float],
    ) -> Network:
        segment_weights = territory_data.segment_weights
        if segment_weights is None:
            segment_weights = {
                segment_id: 1.0 for segment_id in territory_data.segments.keys()
            }

        # 1. Calculate segment-level criteria grades
        segment_criteria_df = self._collect_segment_criteria_grades(
            territory_data, criteria_types
        )

        # 2. Calculate segment scores
        segment_scores_df = self._calculate_segment_scores(
            segment_criteria_df, criteria_weights
        )

        # 3. Calculate criteria-level scores
        territory_criteria_scores_df = self._calculate_criteria_scores(
            segment_criteria_df, segment_weights
        )

        # 4. Calculate network-level scores
        network_overall_scores_df = self._calculate_territory_overall_scores(
            territory_criteria_scores_df, criteria_weights
        )

        segment_scores = frozenset(
            SegmentScore(
                segment_id=row["segment_id"],
                epoch=row["epoch"],
                vehicle_type=VehicleType.parse_french(row["vehicle_type"]),
                score=row["score"],
                normalized_score=row["normalized_score"],
            )
            for _, row in segment_scores_df.iterrows()
        )

        segment_ids = set(segment_scores_df["segment_id"].unique())
        included_segments = {
            segment_id: segment
            for segment_id, segment in territory_data.segments.items()
            if segment_id in segment_ids
        }

        territory_criteria_scores = frozenset(
            TerritoryCriteriaScore(
                criteria_type=CriteriaType.from_string(row["criteria_type"]),
                epoch=row["epoch"],
                vehicle_type=VehicleType.parse_french(row["vehicle_type"]),
                score=row["score"],
                normalized_score=row["normalized_score"],
            )
            for _, row in territory_criteria_scores_df.iterrows()
        )

        territory_overall_scores = frozenset(
            TerritoryScore(
                epoch=row["epoch"],
                vehicle_type=VehicleType.parse_french(row["vehicle_type"]),
                score=row["score"],
                normalized_score=row["normalized_score"],
            )
            for _, row in network_overall_scores_df.iterrows()
        )

        return Network(
            segments=included_segments,
            segment_scores=segment_scores,
            territory_criteria_scores=territory_criteria_scores,
            territory_overall_scores=territory_overall_scores,
        )

    def _collect_segment_criteria_grades(
        self,
        territory_data: TerritoryData,
        criteria_types: List[CriteriaType],
    ) -> pd.DataFrame:
        criteria_records = []
        for criteria_type in criteria_types:
            calculator = self.strategy_factory.get_calculator(criteria_type)
            thresholds = self.thresholds_config[criteria_type]

            segment_criteria_grades = calculator.calculate(territory_data, thresholds)

            criteria_records.extend(
                [criteria_grade.to_dict() for criteria_grade in segment_criteria_grades]
            )

        return pd.DataFrame(criteria_records)

    def _calculate_segment_scores(
        self,
        segment_criteria_df: pd.DataFrame,
        criteria_weights: Dict[CriteriaType, float],
    ) -> pd.DataFrame:
        weighted_criteria = segment_criteria_df.copy()
        weighted_criteria["weight"] = weighted_criteria["criteria_type"].map(
            criteria_weights
        )
        weighted_criteria["weighted_grade"] = (
            weighted_criteria["grade"] * weighted_criteria["weight"]
        )

        segment_scores = (
            weighted_criteria.groupby(["segment_id", "epoch", "vehicle_type"])
            .agg(
                score=("weighted_grade", "sum"),
                sum_of_weights=("weight", "sum"),
            )
            .reset_index()
        )
        segment_scores["weighted_avg_score"] = (
            segment_scores["score"] / segment_scores["sum_of_weights"]
        )
        min_score = segment_scores["weighted_avg_score"].min()
        max_score = segment_scores["weighted_avg_score"].max()
        if max_score == min_score:
            segment_scores["normalized_score"] = 50
        else:
            segment_scores["normalized_score"] = (
                (segment_scores["weighted_avg_score"] - min_score)
                / (max_score - min_score)
                * 100
            )

        return segment_scores

    def _calculate_criteria_scores(
        self, segment_criteria_df: pd.DataFrame, segment_weights: Dict[int, float]
    ) -> pd.DataFrame:
        """
        Calculate scores for each criteria across all segments.
        Formula: 〖Note〗_critère = (∑_(i=nb tronçon)〖n_i∙m_i∙l_i 〗)/(∑〖m_i∙l_i 〗)
        """
        # Add segment weights to the criteria dataframe
        weighted_segment_criteria = segment_criteria_df.copy()
        weighted_segment_criteria["segment_weight"] = weighted_segment_criteria[
            "segment_id"
        ].map(segment_weights)

        # Calculate weighted score for each segment's criteria
        weighted_segment_criteria["weighted_grade"] = (
            weighted_segment_criteria["grade"]
            * weighted_segment_criteria["segment_weight"]
        )

        # Group by criteria_type, epoch, vehicle_type
        territory_criteria_scores = (
            weighted_segment_criteria.groupby(
                ["criteria_type", "epoch", "vehicle_type"]
            )
            .agg(
                score=("weighted_grade", "sum"),
                total_weight=("segment_weight", "sum"),
            )
            .reset_index()
        )

        territory_criteria_scores["score"] = (
            territory_criteria_scores["score"]
            / territory_criteria_scores["total_weight"]
        )

        normalized_criteria_scores = self._normalize_criteria_scores(
            territory_criteria_scores
        )

        return normalized_criteria_scores

    def _normalize_criteria_scores(
        self, criteria_scores_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Normalize criteria scores using min-max normalization.
        Formula: 〖Note〗_(critère_normal) = (〖Note〗_critère - 〖note〗_min) / (〖note〗_max - 〖note〗_min)
        """
        normalized_results = []
        for criteria_type, criteria_group in criteria_scores_df.groupby(
            "criteria_type"
        ):
            min_score = criteria_group["score"].min()
            max_score = criteria_group["score"].max()

            if max_score == min_score:
                criteria_group["normalized_score"] = (
                    50.0  # Default value when all scores are the same
                )
            else:
                criteria_group["normalized_score"] = (
                    (criteria_group["score"] - min_score)
                    / (max_score - min_score)
                    * 100.0
                )

            normalized_results.append(criteria_group)

        if normalized_results:
            return pd.concat(normalized_results, ignore_index=True)
        else:
            return pd.DataFrame(columns=criteria_scores_df.columns)

    def _calculate_territory_overall_scores(
        self,
        criteria_scores_df: pd.DataFrame,
        criteria_weights: Dict[CriteriaType, float],
    ) -> pd.DataFrame:
        """
        Calculate overall network scores by aggregating criteria scores.
        Formula: Logistiscore = (∑_(i=)〖C_i∙w_i 〗)/(∑w_i)
        """
        criteria_weights_df = pd.DataFrame(
            [
                {"criteria_type": criteria_type, "weight": weight}
                for criteria_type, weight in criteria_weights.items()
            ]
        )
        weighted_criteria_scores = pd.merge(
            criteria_scores_df, criteria_weights_df, on="criteria_type", how="left"
        )

        weighted_criteria_scores["weighted_score"] = (
            weighted_criteria_scores["normalized_score"]
            * weighted_criteria_scores["weight"]
        )

        territory_overall_scores = (
            weighted_criteria_scores.groupby(["epoch", "vehicle_type"])
            .agg(weighted_sum=("weighted_score", "sum"), weight_sum=("weight", "sum"))
            .reset_index()
        )

        territory_overall_scores["score"] = (
            territory_overall_scores["weighted_sum"]
            / territory_overall_scores["weight_sum"]
        )
        territory_overall_scores["normalized_score"] = territory_overall_scores["score"]

        return territory_overall_scores
