import pytest

from logistiscore.ir.criteria import CriteriaType


class TestCriteriaType:
    @pytest.mark.parametrize(
        ["criteria_type"],
        [[criteria_type] for criteria_type in CriteriaType],
    )
    def test_should_get_criteria_for_all_types(
        self, criteria_type: CriteriaType
    ) -> None:
        criteria_value = criteria_type.get_criteria()

        assert isinstance(criteria_value, str)
        assert criteria_value == criteria_type.value

    @pytest.mark.parametrize(
        ["criteria_string", "criteria_type"],
        [
            ("C5", CriteriaType.C5),
        ],
    )
    def test_should_parse_string_for_all_criteria_types(
        self, criteria_string: str, criteria_type: CriteriaType
    ) -> None:
        parsed_criteria_type = CriteriaType.from_string(criteria_string)

        assert parsed_criteria_type == criteria_type

    def test_from_string_raises_key_error_for_unknown_criteria_type(self) -> None:
        with pytest.raises(KeyError):
            CriteriaType.from_string("Unknown criteria")
