import os
from typing import FrozenSet, Optional

import pandas as pd

from logistiscore.ir.slope import SlopeSegmentMeasurement


def load_slope_data_from_cache(
    territory_dir: str, slope_data_filename: str
) -> Optional[FrozenSet[SlopeSegmentMeasurement]]:
    cache_file = os.path.join(territory_dir, slope_data_filename)
    if not os.path.exists(cache_file):
        raise FileNotFoundError(f"Slope data cache file not found: {cache_file}")

    try:
        df = pd.read_csv(cache_file)
        slope_data = {
            SlopeSegmentMeasurement(
                segment_id=row["segment_id"], slope_percentage=row["slope_percentage"]
            )
            for _, row in df.iterrows()
        }
        return frozenset(slope_data)
    except Exception as e:
        raise ValueError(f"Error loading slope data from cache file {cache_file}: {e}")


def save_slope_data_to_cache(
    territory_dir: str,
    slope_data: FrozenSet[SlopeSegmentMeasurement],
    slope_data_filename: str = "slope_data.csv",
) -> None:
    os.makedirs(territory_dir, exist_ok=True)

    cache_file = os.path.join(territory_dir, slope_data_filename)

    data = [
        {
            "segment_id": measurement.segment_id,
            "slope_percentage": measurement.slope_percentage,
        }
        for measurement in slope_data
    ]

    df = pd.DataFrame(data)
    df.to_csv(cache_file, index=False)
