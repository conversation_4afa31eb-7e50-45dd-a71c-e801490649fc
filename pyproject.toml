[build-system]
requires = ["setuptools>=77.0.3"]
build-backend = "setuptools.build_meta"

[project]
name = "logistiscore"
version = "1.0.0"
description = "Outil d'analyse de l'indice de livrabilité"
requires-python = ">=3.11"
dependencies = [
    "geopandas",
    "pandas",
    "retry",
    "requests",
]

[project.optional-dependencies]
test = [
    "pytest",
    "pytest-cov",
]
lint = [
    "black",
    "isort",
    "mypy",
    "flake8",
    "flake8-pyproject",
    "types-shapely",
    "pandas-stubs",
    "types-geopandas",
    "types-requests",
    "types-retry",
]

[tool.setuptools]
packages = ["logistiscore"]

[tool.black]
line-length = 88

[tool.isort]
profile = "black"
line_length = 88

[tool.flake8]
max-line-length = 88
ignore = [
    "E24", "E121", "E123", "E126", "E203", "E226", "E501", "E701", "E704",
    "W291", "W293", "W503", "W504"
]

[tool.mypy]
python_version = "3.11"
files = ["logistiscore/"]

[tool.coverage.run]
source = ["logistiscore"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/conftest.py",
]
