import logging
import math
from typing import Dict, FrozenSet, List, Optional, Set

import requests
from requests.exceptions import RequestException
from retry.api import retry_call

from logistiscore.ir.here_response import (
    HEREAttributesResponse,
    HERERowData,
    HERETileData,
)
from logistiscore.ir.slope import SlopeSegmentMeasurement
from logistiscore.ir.territory import Territory
from logistiscore.ir.tile_layer_mapping import TileLayerMapping
from logistiscore.workers.here_tile_computation import HERETileComputationWorker


class HEREMapsAttributesRepository:
    BASE_URL: str = "https://smap.hereapi.com/v8/maps/attributes"
    MAX_RETRIES: int = 3
    TIMEOUT_SECONDS: int = 30

    def __init__(self, api_key: str):
        self.api_key = api_key

    def _fetch_tiles(self, mappings: Set[TileLayerMapping]) -> HEREAttributesResponse:
        if len(mappings) == 0:
            return HEREAttributesResponse.from_dict({"Tiles": []})

        sorted_mappings = sorted(mappings, key=lambda m: (m.layer, m.tile_id))
        params = self._build_request_params(sorted_mappings)

        response_data = retry_call(
            lambda: self._make_request(params),
            tries=self.MAX_RETRIES,
            delay=1,
            backoff=2,
            exceptions=(RequestException,),
        )

        return HEREAttributesResponse.from_dict(response_data)

    def _extract_slope_data_from_response(
        self, response: HEREAttributesResponse, target_segments: List[int]
    ) -> FrozenSet[SlopeSegmentMeasurement]:
        target_link_set = set(segment_id for segment_id in target_segments)
        slope_data = set()

        for tile in response.tiles:
            slope_segments = self._extract_slope_segments_from_tile(
                tile, target_link_set
            )
            slope_data.update(slope_segments)

        return frozenset(slope_data)

    def _build_request_params(self, mappings: List[TileLayerMapping]) -> Dict[str, str]:
        layers = [mapping.layer for mapping in mappings]
        tile_ids = [mapping.tile_id for mapping in mappings]

        return {
            "layers": ",".join(layers),
            "in": "tile:" + ",".join(str(tid) for tid in tile_ids),
            "apiKey": self.api_key,
        }

    def _make_request(self, params: Dict[str, str]) -> Dict[str, List[Dict]]:
        response = requests.get(
            self.BASE_URL,
            params=params,
            timeout=self.TIMEOUT_SECONDS,
        )

        if response.status_code == 200:
            return response.json()
        elif response.status_code == 404:
            logging.warning(
                "No tiles found for the given parameters. Returning empty response."
            )
            return {"Tiles": []}
        else:
            raise RequestException(
                f"Failed to fetch tiles: {response.status_code} - {response.text}"
            )

    def _extract_slope_segments_from_tile(
        self, tile: HERETileData, target_link_set: Set[int]
    ) -> Set[SlopeSegmentMeasurement]:
        """
        Extracts slope segments from a tile, filtering by target link IDs.
        SLOPES: they are represented as vertical road direction [10^-3 degree]
        at coordinate points along the link, when driving from Reference Node.
        Values are comma separated. Each value is relative to the previous.
        Unknown slope values are represented using string value of '-0',
        which is different from an actual slope value of 0.
        """
        slope_segments = set()

        for row in tile.rows:
            if row.link_id is not None and row.link_id in target_link_set:
                segment = self._parse_max_slope_segment_from_row(row)
                if segment is not None:
                    slope_segments.add(segment)

        return slope_segments

    def _parse_max_slope_segment_from_row(
        self, row: HERERowData
    ) -> Optional[SlopeSegmentMeasurement]:
        if not row.slopes or row.link_id is None:
            return None

        try:
            slope_values = self._parse_slope_values_from_string(row.slopes)
            if not slope_values:
                return None

            max_slope = self._find_max_absolute_slope(slope_values)
            if max_slope is None:
                return SlopeSegmentMeasurement(
                    segment_id=row.link_id,
                    slope_percentage=None,
                )

            return self._create_slope_segment(max_slope, row.link_id)
        except ValueError:
            return None

    def _parse_slope_values_from_string(self, slopes_str: str) -> List[Optional[int]]:
        slope_values: List[Optional[int]] = []
        current_slope = 0

        for slope_part in slopes_str.split(","):
            stripped = slope_part.strip()
            if len(stripped) == 0:
                continue

            if stripped == "-0":
                slope_values.append(None)
            else:
                try:
                    relative_slope = int(stripped)
                    current_slope += relative_slope
                    slope_values.append(current_slope)
                except ValueError:
                    logging.warning(
                        f"Invalid slope value '{stripped}' in SLOPES string: "
                        f"{slopes_str}"
                    )
                    slope_values.append(None)

        return slope_values

    def _find_max_absolute_slope(
        self, slope_values: List[Optional[int]]
    ) -> Optional[int]:
        valid_slopes = [slope for slope in slope_values if slope is not None]
        if not valid_slopes:
            return None

        return max(valid_slopes, key=abs)

    def _create_slope_segment(
        self, slope_millideg: int, segment_id: int
    ) -> SlopeSegmentMeasurement:
        if abs(slope_millideg) >= 90000:
            slope_percentage = (
                11459156.0  # ~= 2 * math.tan(math.radians(89999/1000)) * 100
            )
            logging.warning(
                f"Extreme slope value {slope_millideg} for segment {segment_id}, "
                f"setting to maximum percentage {slope_percentage}."
            )
        else:
            slope_deg = abs(slope_millideg / 1000)
            slope_percentage = math.tan(math.radians(slope_deg)) * 100

        return SlopeSegmentMeasurement(
            segment_id=segment_id,
            slope_percentage=slope_percentage,
        )

    def get_maps_attribute_data(
        self, mappings: Set[TileLayerMapping], target_segments: List[int]
    ) -> FrozenSet[SlopeSegmentMeasurement]:
        response = self._fetch_tiles(mappings)
        return self._extract_slope_data_from_response(response, target_segments)


def compute_slope_data(
    repository: HEREMapsAttributesRepository,
    segments: List[int],
    territory: Territory,
    tile_worker: HERETileComputationWorker,
) -> FrozenSet[SlopeSegmentMeasurement]:
    tile_mappings = tile_worker.compute_tiles_for_territory(territory)
    return repository.get_maps_attribute_data(tile_mappings, segments)
