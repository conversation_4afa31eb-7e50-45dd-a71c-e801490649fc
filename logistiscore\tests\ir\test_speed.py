from logistiscore.tests.factories import (
    create_averaged_segment_speed_measurement,
    create_averaged_speed_data,
)


class TestAveragedSpeedData:
    def test_to_dict_with_measurements(self):
        measurements = [
            create_averaged_segment_speed_measurement(
                segment_id="1", epoch=1, avg_speed=30.0, total_count=10, freeflow=50.0
            ),
            create_averaged_segment_speed_measurement(
                segment_id="2", epoch=1, avg_speed=20.0, total_count=5, freeflow=40.0
            ),
        ]

        data = create_averaged_speed_data(measurements=measurements)

        result = data.to_dict()

        assert result == {
            "segment_id": ["1", "2"],
            "epoch": [1, 1],
            "avg_speed": [30.0, 20.0],
            "total_count": [10, 5],
            "freeflow": [50.0, 40.0],
        }

    def test_to_dict_with_empty_measurements(self):
        data = create_averaged_speed_data(measurements=[])

        result = data.to_dict()

        assert result == {
            "segment_id": [],
            "epoch": [],
            "avg_speed": [],
            "total_count": [],
            "freeflow": [],
        }
