import logging
import os
from datetime import datetime
from typing import Dict, FrozenSet, Generator, List, Optional

import geopandas
import pyogrio  # type: ignore
from shapely.geometry import Polygon

from logistiscore.ir.epoch import EpochInterval
from logistiscore.ir.network import Segment
from logistiscore.ir.slope import SlopeSegmentMeasurement
from logistiscore.ir.speed import AveragedSpeedData
from logistiscore.ir.territory import Territory, TerritoryData
from logistiscore.repositories.here_fcd_repository import HEREFCDRepository
from logistiscore.repositories.here_maps_attributes_repository import (
    HEREMapsAttributesRepository,
    compute_slope_data,
)
from logistiscore.repositories.here_street_network_repository import (
    HEREStreetNetworkRepository,
)
from logistiscore.serializers.cache_slope_data import (
    load_slope_data_from_cache,
    save_slope_data_to_cache,
)
from logistiscore.workers.avg_speed_data_computer import (
    aggregate_directions_by_segment_epoch,
    min_segment_speed_aggregator,
)
from logistiscore.workers.compute_segments_weights import compute_segments_weights
from logistiscore.workers.here_tile_computation import HERETileComputationWorker


class TerritoryBuilder:
    def __init__(
        self,
        territories_dir: str,
        here_api_key: str,
        segments_filename: str = "segments.geojson",
        speed_data_filename: str = "speed_data.csv",
        slope_data_filename: str = "slope_data.csv",
        perimeter_filename: str = "perimeter.geojson",
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        epoch_interval: Optional[EpochInterval] = None,
    ) -> None:
        self.territories_dir = territories_dir
        self.here_api_key = here_api_key
        self.segments_filename = segments_filename
        self.speed_data_filename = speed_data_filename
        self.slope_data_filename = slope_data_filename
        self.perimeter_filename = perimeter_filename
        self.start_date = start_date
        self.end_date = end_date
        self.epoch_interval = epoch_interval
        self.territories: List[Territory] = []

    def build_all_territory_data(self) -> Generator[TerritoryData, None, None]:
        territory_names = self.get_all_territory_names()
        logging.info(f"Found {len(territory_names)} territories")

        for name in territory_names:
            logging.info(f"Processing territory {name}")
            territory = self.parse_territory_shape(name)
            yield self.build_territory_data(territory)

    def build(self) -> None:
        territory_names = self.get_all_territory_names()
        for name in territory_names:
            logging.info(f"Parsing territory {name}")
            territory = self.parse_territory_shape(name)
            self.territories.append(territory)

    def parse_territory_shape(self, territory_name: str) -> Territory:
        territory_dir = self._get_territory_directory(territory_name)
        file_path = os.path.join(territory_dir, self.perimeter_filename)
        try:
            gdf = geopandas.read_file(file_path)
        except pyogrio.errors.DataSourceError as e:
            raise FileNotFoundError(f"Could not read {file_path}\n{e}")
        projected = gdf.to_crs("EPSG:4326")

        if projected.empty:
            raise ValueError(f"No valid geometries found in {file_path}")
        if len(projected) > 1:
            raise ValueError(f"Multiple geometries found in {file_path}. Expected one.")
        geometry = projected.geometry.iloc[0]
        if geometry.is_empty:
            raise ValueError(f"Geometry in {file_path} is empty.")
        if isinstance(geometry, Polygon):
            territory = Territory(name=territory_name, geometry=geometry)
            logging.info(f"Parsed territory: {territory.name}")
            return territory
        else:
            raise ValueError(
                f"Geometry in {file_path} is not a Polygon. Found: {type(geometry)}"
            )

    def build_territory_data(self, territory: Territory) -> TerritoryData:
        logging.info(f"Building territory data for {territory.name}")

        territory_dir = self._get_territory_directory(territory.name)
        segments = self._load_territory_segments(territory_dir)
        speed_data = self._load_territory_speed_data(territory_dir, segments)
        slope_data = self._load_territory_slope_data(territory, list(segments.keys()))
        segment_weights = compute_segments_weights(segments=segments)

        logging.info(
            f"Territory {territory.name}: {len(segments)} segments, "
            f"{len(speed_data.measurements)} speed measurements"
        )

        return TerritoryData(
            territory=territory,
            segments=segments,
            speed_data=speed_data,
            slope_data=slope_data,
            segment_weights=segment_weights,
        )

    def get_territories(self) -> List[Territory]:
        return self.territories

    def get_all_territory_names(self) -> List[str]:
        territory_names = []
        for dir_entry in os.scandir(self.territories_dir):
            if dir_entry.is_dir():
                territory_names.append(dir_entry.name)
        territory_names.sort()
        return territory_names

    def _get_territory_directory(self, territory_name: str) -> str:
        territory_dir = os.path.join(self.territories_dir, territory_name)
        if not os.path.exists(territory_dir):
            raise FileNotFoundError(f"Territory directory not found: {territory_dir}")
        return territory_dir

    def _load_territory_segments(self, territory_dir: str) -> Dict[int, Segment]:
        segments_file = os.path.join(territory_dir, self.segments_filename)
        if not os.path.exists(segments_file):
            raise FileNotFoundError(f"Segments file not found: {segments_file}")

        logging.info(f"Loading segments from {segments_file}")
        street_network_repository = HEREStreetNetworkRepository()
        segments = street_network_repository.load_network(segments_file)
        logging.info(f"Loaded {len(segments)} segments")
        return segments

    def _load_territory_speed_data(
        self, territory_dir: str, segments: Dict[int, Segment]
    ) -> AveragedSpeedData:
        speed_data_file = os.path.join(territory_dir, self.speed_data_filename)
        if not os.path.exists(speed_data_file):
            raise FileNotFoundError(f"Speed data file not found: {speed_data_file}")

        logging.info(f"Loading speed data from {speed_data_file}")
        fcd_repository = HEREFCDRepository(speed_data_file)
        speed_data = fcd_repository.get_averaged_speed_data(
            segment_ids=list(segments.keys()),
            start_date=self.start_date,
            end_date=self.end_date,
            epoch_interval=self.epoch_interval,
        )
        speed_data = aggregate_directions_by_segment_epoch(
            speed_data=speed_data,
            aggregator=min_segment_speed_aggregator(),
        )
        logging.info(f"Loaded {len(speed_data.measurements)} speed measurements")
        return speed_data

    def _load_territory_slope_data(
        self, territory: Territory, segments: List[int]
    ) -> frozenset[SlopeSegmentMeasurement]:
        territory_dir = self._get_territory_directory(territory.name)
        slope_data_file = os.path.join(territory_dir, self.slope_data_filename)
        logging.info(f"Loading slope data from cache {slope_data_file}")
        slope_data: Optional[FrozenSet[SlopeSegmentMeasurement]] = None
        try:
            slope_data = load_slope_data_from_cache(
                territory_dir=territory_dir,
                slope_data_filename=self.slope_data_filename,
            )
        except FileNotFoundError:
            logging.info(f"Slope data cache file not found: {slope_data_file}")
        except ValueError as e:
            logging.error(f"Error loading slope data from cache: {e}")
        if slope_data is None:
            map_attributes_repository = HEREMapsAttributesRepository(self.here_api_key)
            tile_worker = HERETileComputationWorker()
            slope_data = compute_slope_data(
                repository=map_attributes_repository,
                segments=segments,
                territory=territory,
                tile_worker=tile_worker,
            )
            if len(slope_data) == 0:
                logging.warning(
                    f"No slope data found for territory {territory.name} on segments {segments}"
                )
            else:
                save_slope_data_to_cache(
                    territory_dir, slope_data, self.slope_data_filename
                )
        return slope_data
