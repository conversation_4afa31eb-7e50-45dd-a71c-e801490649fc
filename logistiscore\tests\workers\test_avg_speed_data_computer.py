import pytest

from logistiscore.tests.factories import (
    create_averaged_segment_speed_measurement,
    create_averaged_speed_data,
)
from logistiscore.workers.avg_speed_data_computer import (
    aggregate_directions_by_segment_epoch,
    min_segment_speed_aggregator,
)


class TestMinSegmentSpeedAggregator:
    def test_single_measurement_returns_same_measurement(self) -> None:
        measurement = create_averaged_segment_speed_measurement(avg_speed=45.0)
        aggregator = min_segment_speed_aggregator()

        result = aggregator([measurement])

        assert result == measurement

    def test_multiple_measurements_returns_minimum_speed_with_summed_count(
        self,
    ) -> None:
        measurement1 = create_averaged_segment_speed_measurement(
            avg_speed=60.0, total_count=5, direction="F"
        )
        measurement2 = create_averaged_segment_speed_measurement(
            avg_speed=40.0, total_count=8, direction="T"
        )
        aggregator = min_segment_speed_aggregator()

        result = aggregator([measurement1, measurement2])

        expected_measurement = create_averaged_segment_speed_measurement(
            avg_speed=40.0, total_count=13, direction="T"
        )
        assert result == expected_measurement

    def test_measurements_with_same_min_speed_returns_first_occurrence(self) -> None:
        measurement1 = create_averaged_segment_speed_measurement(
            avg_speed=40.0, total_count=5, direction="F"
        )
        measurement2 = create_averaged_segment_speed_measurement(
            avg_speed=40.0, total_count=7, direction="T"
        )
        aggregator = min_segment_speed_aggregator()

        result = aggregator([measurement1, measurement2])

        expected_measurement = create_averaged_segment_speed_measurement(
            avg_speed=40.0, total_count=12, direction="F"
        )
        assert result == expected_measurement

    def test_empty_list_raises_value_error(self) -> None:
        aggregator = min_segment_speed_aggregator()

        with pytest.raises(ValueError):
            aggregator([])


class TestAggregateDirectionsBySegmentEpoch:
    def test_single_measurement_per_segment_epoch_returns_unchanged(self) -> None:
        measurement = create_averaged_segment_speed_measurement()
        speed_data = create_averaged_speed_data(measurements=(measurement,))

        result = aggregate_directions_by_segment_epoch(speed_data)

        assert result.measurements == (measurement,)

    def test_empty_measurements_returns_empty_result(self) -> None:
        speed_data = create_averaged_speed_data(measurements=())

        result = aggregate_directions_by_segment_epoch(speed_data)

        assert len(result.measurements) == 0
        assert result.epoch_type == speed_data.epoch_type

    def test_complex_grouping_scenario(self) -> None:
        measurements = (
            create_averaged_segment_speed_measurement(10000, 8, 50.0, 5),
            create_averaged_segment_speed_measurement(10000, 8, 30.0, 3),
            create_averaged_segment_speed_measurement(10000, 10, 40.0, 7),
            create_averaged_segment_speed_measurement(20000, 8, 60.0, 2),
            create_averaged_segment_speed_measurement(20000, 10, 70.0, 4),
            create_averaged_segment_speed_measurement(20000, 10, 55.0, 6),
        )
        speed_data = create_averaged_speed_data(measurements=measurements)

        result = aggregate_directions_by_segment_epoch(speed_data)

        expected_speed_data_measurements = (
            create_averaged_segment_speed_measurement(10000, 8, 30.0, 8),
            create_averaged_segment_speed_measurement(10000, 10, 40.0, 7),
            create_averaged_segment_speed_measurement(20000, 8, 60.0, 2),
            create_averaged_segment_speed_measurement(20000, 10, 55.0, 10),
        )
        assert result.measurements == expected_speed_data_measurements
        assert result.epoch_type == speed_data.epoch_type
