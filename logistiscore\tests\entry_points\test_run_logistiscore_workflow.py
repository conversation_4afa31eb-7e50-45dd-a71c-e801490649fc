from datetime import datetime
from typing import Any, Dict
from unittest.mock import Mock, call, patch

from logistiscore.constants import CRITERIA_WEIGHTS
from logistiscore.entry_points.run_logistiscore_workflow import (
    execute_script,
    main,
    parse_args,
    run_workflow,
)
from logistiscore.ir.criteria import CriteriaType
from logistiscore.ir.epoch import EpochInterval
from logistiscore.tests.factories import (
    create_network,
    create_territory,
    create_territory_data,
)


class TestArgParser:
    def test_parse_args_with_required_arguments(self) -> None:
        test_args = [
            "--territories-dir",
            "territories",
            "--here-api-key",
            "test_api_key",
            "--output-network",
            "output",
        ]
        with patch("sys.argv", ["script.py"] + test_args):
            args = parse_args(test_args)

        assert args.territories_dir == "territories"
        assert args.here_api_key == "test_api_key"
        assert args.output_network == "output"
        assert args.segments_filename == "segments.geojson"
        assert args.speed_data_filename == "speed_data.csv"
        assert args.perimeter_filename == "perimeter.geojson"
        assert args.criteria_types == ["C5"]
        assert args.log_level == "INFO"
        assert args.start_date is None
        assert args.end_date is None
        assert args.start_epoch is None
        assert args.end_epoch is None

    def test_parse_args_with_all_arguments(self) -> None:
        test_args = [
            "--territories-dir",
            "territories",
            "--here-api-key",
            "test_api_key",
            "--output-network",
            "output",
            "--segments-filename",
            "test_segments.geojson",
            "--speed-data-filename",
            "test_speed.csv",
            "--perimeter-filename",
            "test_perimeter.geojson",
            "--start-date",
            "2025-03-01",
            "--end-date",
            "2025-03-31",
            "--start-epoch",
            "8",
            "--end-epoch",
            "18",
            "--criteria-types",
            "C1",
            "C5",
            "--log-level",
            "DEBUG",
        ]

        args = parse_args(test_args)

        assert args.territories_dir == "territories"
        assert args.here_api_key == "test_api_key"
        assert args.output_network == "output"
        assert args.segments_filename == "test_segments.geojson"
        assert args.speed_data_filename == "test_speed.csv"
        assert args.perimeter_filename == "test_perimeter.geojson"
        assert args.start_date == "2025-03-01"
        assert args.end_date == "2025-03-31"
        assert args.start_epoch == "8"
        assert args.end_epoch == "18"
        assert args.criteria_types == ["C1", "C5"]
        assert args.log_level == "DEBUG"


class TestRunWorkflow:
    def setup_workflow_mocks(self) -> Dict[str, Any]:
        mocks: Dict[str, Any] = {}
        mocks["territory_builder"] = Mock()
        mocks["territory_data_list"] = [
            create_territory_data(territory=create_territory(name="territory1")),
            create_territory_data(territory=create_territory(name="territory2")),
        ]
        mocks["territory_builder"].build_all_territory_data.return_value = iter(
            mocks["territory_data_list"]
        )
        mocks["calculator_factory"] = Mock()
        mocks["score_calculator"] = Mock()
        mocks["network"] = create_network()
        mocks["score_calculator"].calculate_network.return_value = mocks["network"]

        return mocks

    @patch("logistiscore.entry_points.run_logistiscore_workflow.TerritoryBuilder")
    @patch(
        "logistiscore.entry_points.run_logistiscore_workflow."
        "CriteriaCalculatorFactory"
    )
    @patch("logistiscore.entry_points.run_logistiscore_workflow.NetworkScoreCalculator")
    @patch("logistiscore.entry_points.run_logistiscore_workflow.save_network")
    def test_run_workflow_loads_segments_and_speed_data(
        self,
        mock_save_network: Any,
        mock_score_calculator_class: Any,
        mock_calculator_factory_class: Any,
        mock_territory_builder_class: Any,
        two_valid_territories: Any,
    ) -> None:
        mocks = self.setup_workflow_mocks()
        mock_territory_builder_class.return_value = mocks["territory_builder"]
        mock_calculator_factory_class.return_value = mocks["calculator_factory"]
        mock_score_calculator_class.return_value = mocks["score_calculator"]

        territories_dir = str(two_valid_territories)
        output_folder = "test_output"
        criteria_types = [CriteriaType.C5]
        segments_filename = "segments.geojson"
        speed_data_filename = "speed_data.csv"
        perimeter_filename = "perimeter.geojson"

        run_workflow(
            territories_dir=territories_dir,
            here_api_key="",
            output_network_folder=output_folder,
            criteria_types=criteria_types,
            segments_filename=segments_filename,
            speed_data_filename=speed_data_filename,
            perimeter_filename=perimeter_filename,
        )

        mock_territory_builder_class.assert_called_once_with(
            territories_dir=territories_dir,
            here_api_key="",
            segments_filename=segments_filename,
            speed_data_filename=speed_data_filename,
            perimeter_filename=perimeter_filename,
            start_date=None,
            end_date=None,
            epoch_interval=None,
        )
        mocks["territory_builder"].build_all_territory_data.assert_called_once()

    @patch("logistiscore.entry_points.run_logistiscore_workflow.TerritoryBuilder")
    @patch(
        "logistiscore.entry_points.run_logistiscore_workflow."
        "CriteriaCalculatorFactory"
    )
    @patch("logistiscore.entry_points.run_logistiscore_workflow.NetworkScoreCalculator")
    @patch("logistiscore.entry_points.run_logistiscore_workflow.save_network")
    def test_run_workflow_calculates_and_saves_network(
        self,
        mock_save_network: Any,
        mock_score_calculator_class: Any,
        mock_calculator_factory_class: Any,
        mock_territory_builder_class: Any,
        two_valid_territories: Any,
    ) -> None:
        mocks = self.setup_workflow_mocks()
        mock_territory_builder_class.return_value = mocks["territory_builder"]
        mock_calculator_factory_class.return_value = mocks["calculator_factory"]
        mock_score_calculator_class.return_value = mocks["score_calculator"]

        territories_dir = str(two_valid_territories)
        output_folder = "test_output"
        criteria_types = [CriteriaType.C5]
        segments_filename = "segments.geojson"
        speed_data_filename = "speed_data.csv"
        perimeter_filename = "perimeter.geojson"

        run_workflow(
            territories_dir=territories_dir,
            here_api_key="",
            output_network_folder=output_folder,
            criteria_types=criteria_types,
            segments_filename=segments_filename,
            speed_data_filename=speed_data_filename,
            perimeter_filename=perimeter_filename,
        )

        mocks["score_calculator"].calculate_network.assert_has_calls(
            [
                call(
                    territory_data=mocks["territory_data_list"][0],
                    criteria_types=criteria_types,
                    criteria_weights=CRITERIA_WEIGHTS,
                ),
                call(
                    territory_data=mocks["territory_data_list"][1],
                    criteria_types=criteria_types,
                    criteria_weights=CRITERIA_WEIGHTS,
                ),
            ],
            any_order=True,
        )

        # Verify save_network was called for each territory
        mock_save_network.assert_has_calls(
            [
                call(
                    mocks["network"],
                    output_folder,
                    "territory1",
                ),
                call(
                    mocks["network"],
                    output_folder,
                    "territory2",
                ),
            ],
            any_order=True,
        )

    @patch("logistiscore.entry_points.run_logistiscore_workflow.TerritoryBuilder")
    @patch(
        "logistiscore.entry_points.run_logistiscore_workflow."
        "CriteriaCalculatorFactory"
    )
    @patch("logistiscore.entry_points.run_logistiscore_workflow.NetworkScoreCalculator")
    @patch("logistiscore.entry_points.run_logistiscore_workflow.save_network")
    def test_run_workflow_applies_date_filtering(
        self,
        mock_save_network: Any,
        mock_score_calculator_class: Any,
        mock_calculator_factory_class: Any,
        mock_territory_builder_class: Any,
        two_valid_territories: Any,
    ) -> None:
        mocks = self.setup_workflow_mocks()
        mock_territory_builder_class.return_value = mocks["territory_builder"]
        mock_calculator_factory_class.return_value = mocks["calculator_factory"]
        mock_score_calculator_class.return_value = mocks["score_calculator"]

        territories_dir = str(two_valid_territories)
        output_folder = "test_output"
        criteria_types = [CriteriaType.C5]
        segments_filename = "segments.geojson"
        speed_data_filename = "speed_data.csv"
        perimeter_filename = "perimeter.geojson"
        start_date = datetime(2025, 1, 1)
        end_date = datetime(2025, 1, 31)
        epoch_interval = EpochInterval(start_epoch=8, end_epoch=18)

        run_workflow(
            territories_dir=territories_dir,
            here_api_key="",
            output_network_folder=output_folder,
            criteria_types=criteria_types,
            segments_filename=segments_filename,
            speed_data_filename=speed_data_filename,
            perimeter_filename=perimeter_filename,
            start_date=start_date,
            end_date=end_date,
            epoch_interval=epoch_interval,
        )

        mock_territory_builder_class.assert_called_once_with(
            territories_dir=territories_dir,
            here_api_key="",
            segments_filename=segments_filename,
            speed_data_filename=speed_data_filename,
            perimeter_filename=perimeter_filename,
            start_date=start_date,
            end_date=end_date,
            epoch_interval=epoch_interval,
        )


class TestMainFunction:
    @patch("logistiscore.entry_points.run_logistiscore_workflow.run_workflow")
    @patch("logistiscore.entry_points.run_logistiscore_workflow.parse_args")
    def test_main_converts_criteria_types_correctly(
        self,
        mock_parse_args: Any,
        mock_run_workflow: Any,
    ) -> None:
        mock_args = Mock()
        mock_args.log_level = "INFO"
        mock_args.criteria_types = ["C5"]
        mock_args.territories_dir = "territories"
        mock_args.output_network = "output"
        mock_args.segments_filename = "segments.geojson"
        mock_args.speed_data_filename = "speed_data.csv"
        mock_args.perimeter_filename = "perimeter.geojson"
        mock_args.start_date = None
        mock_args.end_date = None
        mock_args.start_epoch = None
        mock_args.end_epoch = None

        mock_parse_args.return_value = mock_args

        with patch(
            "logistiscore.entry_points.run_logistiscore_workflow.logging.basicConfig"
        ):
            main()

        mock_run_workflow.assert_called_once()
        _, kwargs = mock_run_workflow.call_args

        assert len(kwargs["criteria_types"]) == 1
        assert isinstance(kwargs["criteria_types"][0], CriteriaType)
        assert kwargs["criteria_types"][0] == CriteriaType.C5

    @patch("logistiscore.entry_points.run_logistiscore_workflow.run_workflow")
    @patch("logistiscore.entry_points.run_logistiscore_workflow.parse_args")
    def test_main_parses_date_parameters_correctly(
        self,
        mock_parse_args: Any,
        mock_run_workflow: Any,
    ) -> None:
        mock_args = Mock()
        mock_args.log_level = "INFO"
        mock_args.criteria_types = ["C5"]
        mock_args.territories_dir = "territories"
        mock_args.output_network = "output"
        mock_args.segments_filename = "segments.geojson"
        mock_args.speed_data_filename = "speed_data.csv"
        mock_args.perimeter_filename = "perimeter.geojson"
        mock_args.start_date = "2025-03-01"
        mock_args.end_date = "2025-03-31"
        mock_args.start_epoch = None
        mock_args.end_epoch = None

        mock_parse_args.return_value = mock_args

        with patch(
            "logistiscore.entry_points.run_logistiscore_workflow.logging.basicConfig"
        ):
            main()

        mock_run_workflow.assert_called_once()
        _, kwargs = mock_run_workflow.call_args

        assert kwargs["start_date"].strftime("%Y-%m-%d") == "2025-03-01"
        assert kwargs["end_date"].strftime("%Y-%m-%d") == "2025-03-31"
        assert isinstance(kwargs["epoch_interval"], EpochInterval)
        assert kwargs["epoch_interval"].start_epoch == 0
        assert kwargs["epoch_interval"].end_epoch == 23

    @patch("logistiscore.entry_points.run_logistiscore_workflow.run_workflow")
    @patch("logistiscore.entry_points.run_logistiscore_workflow.parse_args")
    def test_main_parses_epoch_parameters_correctly(
        self,
        mock_parse_args: Any,
        mock_run_workflow: Any,
    ) -> None:
        mock_args = Mock()
        mock_args.log_level = "INFO"
        mock_args.criteria_types = ["C5"]
        mock_args.territories_dir = "territories"
        mock_args.output_network = "output"
        mock_args.segments_filename = "segments.geojson"
        mock_args.speed_data_filename = "speed_data.csv"
        mock_args.perimeter_filename = "perimeter.geojson"
        mock_args.start_date = None
        mock_args.end_date = None
        mock_args.start_epoch = "8"
        mock_args.end_epoch = "18"

        mock_parse_args.return_value = mock_args

        with patch(
            "logistiscore.entry_points.run_logistiscore_workflow.logging.basicConfig"
        ):
            main()

        mock_run_workflow.assert_called_once()
        _, kwargs = mock_run_workflow.call_args

        assert kwargs["start_date"] is None
        assert kwargs["end_date"] is None
        assert isinstance(kwargs["epoch_interval"], EpochInterval)
        assert kwargs["epoch_interval"].start_epoch == 8
        assert kwargs["epoch_interval"].end_epoch == 18


class TestExecuteScript:
    @patch("logistiscore.entry_points.run_logistiscore_workflow.main")
    def test_execute_script_calls_main_when_name_is_main(self, mock_main: Any) -> None:
        execute_script("__main__")
        mock_main.assert_called_once_with(None)

    @patch("logistiscore.entry_points.run_logistiscore_workflow.main")
    def test_execute_script_passes_args_to_main(self, mock_main: Any) -> None:
        test_args = [
            "--territories-dir",
            "territories",
            "--output-network",
            "output",
        ]
        execute_script("__main__", test_args)
        mock_main.assert_called_once_with(test_args)

    @patch("logistiscore.entry_points.run_logistiscore_workflow.main")
    def test_execute_script_does_nothing_when_name_is_not_main(
        self, mock_main: Any
    ) -> None:
        execute_script("not_main")
        mock_main.assert_not_called()

    @patch("logistiscore.entry_points.run_logistiscore_workflow.main")
    @patch("logistiscore.entry_points.run_logistiscore_workflow.logging.error")
    @patch("logistiscore.entry_points.run_logistiscore_workflow.sys.exit")
    def test_execute_script_handles_exceptions(
        self,
        mock_sys_exit: Any,
        mock_logging_error: Any,
        mock_main: Any,
    ) -> None:
        mock_exception = Exception("Test error")
        mock_main.side_effect = mock_exception

        execute_script("__main__")

        mock_logging_error.assert_called_once_with(mock_exception)
        mock_sys_exit.assert_called_once_with(2)
