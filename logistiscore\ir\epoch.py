from dataclasses import dataclass
from enum import Enum


class EpochType(Enum):
    EPOCH_15MIN = "EPOCH-15MIN"
    EPOCH_5MIN = "EPOCH-5MIN"
    EPOCH_60MIN = "EPOCH-60MIN"
    UNKNOWN = "UNKNOWN"

    @staticmethod
    def from_string(epoch: str) -> "EpochType":
        return {
            "EPOCH-15MIN": EpochType.EPOCH_15MIN,
            "EPOCH-5MIN": EpochType.EPOCH_5MIN,
            "EPOCH-60MIN": EpochType.EPOCH_60MIN,
        }.get(epoch, EpochType.UNKNOWN)

    def get_code(self) -> str:
        return self.value


@dataclass
class EpochInterval:
    start_epoch: int
    end_epoch: int
