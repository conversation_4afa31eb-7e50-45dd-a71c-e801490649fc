import math
from typing import List, Set

from logistiscore.ir.territory import GeographicBounds, Territory
from logistiscore.ir.tile_layer_mapping import TileLayerMapping


class HERETileComputationWorker:
    """
    Computes HERE tile information using hierarchical quadtree-based tiling scheme.

    The HERE tiling scheme uses spherical Mercator projection (EPSG:3857)
    where the world is recursively subdivided into quadrants at each zoom level.
    Each tile is identified by a quadkey (string of digits 0-3) representing the path
    through the quadtree hierarchy.

    Quadrant numbering:
    - 0: Top-left (NW)
    - 1: Top-right (NE)
    - 2: Bottom-left (SW)
    - 3: Bottom-right (SE)

    Quadkey length equals zoom level.
    HERE Tile IDs are 64-bit integers computed from quadkeys.
    Latitude range limited to Mercator bounds (~±85.05°). Longitude wraps at ±180°.
    More details on HERE tiling can be found in their documentation:
    https://www.here.com/docs/bundle/introduction-to-mapping-concepts-user-guide/page/topics/here-tiling-scheme.html
    """

    TILE_LEVEL: int = 8
    FUNC_CLASS_LEVELS: List[int] = [1, 2, 3, 4, 5]
    LAYERS_OF_INTEREST: List[str] = ["ADAS_ATTRIB_FC"]

    def compute_tiles_for_bounds(
        self, bounds: GeographicBounds
    ) -> Set[TileLayerMapping]:
        mappings: Set[TileLayerMapping] = set()
        tile_id_list: List[int] = []
        layer_list: List[str] = []

        for func_class in self.FUNC_CLASS_LEVELS:
            tile_level = func_class + self.TILE_LEVEL
            tile_size = self._calculate_tile_size(tile_level)
            lat = bounds.min_latitude
            while lat <= bounds.max_latitude + tile_size:
                long = bounds.min_longitude
                while long <= bounds.max_longitude + tile_size:
                    tile_id_list.append(self.get_tile_id(lat, long, tile_level))
                    long += tile_size
                    layer_list.extend(
                        [f"{layer}{func_class}" for layer in self.LAYERS_OF_INTEREST]
                    )
                lat += tile_size

        for tile_id, layer in zip(tile_id_list, layer_list):
            mappings.add(TileLayerMapping(tile_id=tile_id, layer=layer))

        return mappings

    def get_tile_id(self, latitude: float, longitude: float, level: int) -> int:
        tile_size = self._calculate_tile_size(level)
        tile_x = math.floor((longitude + 180) / tile_size)
        tile_y = math.floor((latitude + 90) / tile_size)
        return self._calculate_tile_id(tile_x, tile_y, level)

    def compute_tiles_for_territory(
        self, territory: Territory
    ) -> Set[TileLayerMapping]:
        return self.compute_tiles_for_bounds(territory.bounds)

    def _calculate_tile_size(self, level: int) -> float:
        return 180 / math.pow(2, level)

    def _calculate_tile_id(self, tile_x: int, tile_y: int, level: int) -> int:
        return int(tile_y * 2 * math.pow(2, level) + tile_x)
