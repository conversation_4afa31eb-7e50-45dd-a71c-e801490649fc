from logistiscore.tests.factories import create_slope_segment_measurement


class TestSlopeSegmentMeasurement:
    def test_to_dict_should_return_correct_dict(self) -> None:
        segment_measurement = create_slope_segment_measurement(
            segment_id=1000, slope_percentage=5.0
        )

        result = segment_measurement.to_dict()

        expected = {
            "segment_id": 1000,
            "slope_percentage": 5.0,
        }
        assert result == expected

    def test_to_dict_with_none_slope_percentage_should_return_none(self) -> None:
        segment_measurement = create_slope_segment_measurement(
            segment_id=1000, slope_percentage=None
        )

        result = segment_measurement.to_dict()

        expected = {
            "segment_id": 1000,
            "slope_percentage": None,
        }
        assert result == expected
