from typing import Dict, FrozenSet

import pytest

from logistiscore.calculators.criteria_calculator import (
    C5SpeedCriteriaCalculationStrategy,
    C11SlopeCriteriaCalculationStrategy,
    CriteriaCalculationStrategy,
    CriteriaCalculatorFactory,
)
from logistiscore.ir.criteria import CriteriaType, Threshold
from logistiscore.ir.slope import SlopeSegmentMeasurement
from logistiscore.ir.vehicle import VehicleType
from logistiscore.tests.factories import (
    create_averaged_segment_speed_measurement,
    create_averaged_speed_data,
    create_criteria_grade,
    create_segment,
    create_slope_segment_measurement,
    create_territory_data,
    create_threshold,
)


class TestC5SpeedCriteriaCalculationStrategy:
    def test_calculate_assigns_proper_grades(self) -> None:
        segments = {
            10000: create_segment(segment_id=10000),
            20000: create_segment(segment_id=20000),
            30000: create_segment(segment_id=30000),
        }
        measurements = (
            create_averaged_segment_speed_measurement(segment_id=10000, avg_speed=10.0),
            create_averaged_segment_speed_measurement(segment_id=20000, avg_speed=20.0),
            create_averaged_segment_speed_measurement(segment_id=30000, avg_speed=40.0),
        )
        speed_data = create_averaged_speed_data(measurements=measurements)
        thresholds = {VehicleType.PL: create_threshold(good=30.0, bad=15.0)}
        territory_data = create_territory_data(segments=segments, speed_data=speed_data)
        strategy = C5SpeedCriteriaCalculationStrategy()

        criteria_grades = strategy.calculate(territory_data, thresholds)

        expected_grades = {
            create_criteria_grade(
                segment_id=10000,
                criteria_type=CriteriaType.C5,
                vehicle_type=VehicleType.PL,
                epoch=1,
                grade=1.0,
            ),
            create_criteria_grade(
                segment_id=20000,
                criteria_type=CriteriaType.C5,
                vehicle_type=VehicleType.PL,
                epoch=1,
                grade=2.0,
            ),
            create_criteria_grade(
                segment_id=30000,
                criteria_type=CriteriaType.C5,
                vehicle_type=VehicleType.PL,
                epoch=1,
                grade=3.0,
            ),
        }
        assert sorted(list(criteria_grades), key=lambda x: x.segment_id) == sorted(
            list(expected_grades), key=lambda x: x.segment_id
        )

    def test_calculate_handles_multiple_vehicle_types(self) -> None:
        segments = {10000: create_segment(segment_id=10000)}
        measurements = (
            create_averaged_segment_speed_measurement(segment_id=10000, avg_speed=25.0),
        )
        speed_data = create_averaged_speed_data(measurements=measurements)
        thresholds = {
            VehicleType.PL: create_threshold(good=30.0, bad=15.0),
            VehicleType.VUL: create_threshold(good=35.0, bad=20.0),
            VehicleType.VC: create_threshold(good=15.0, bad=10.0),
        }
        territory_data = create_territory_data(segments=segments, speed_data=speed_data)
        strategy = C5SpeedCriteriaCalculationStrategy()

        criteria_grades = strategy.calculate(territory_data, thresholds)

        assert len(criteria_grades) == 3
        criteria_grades_by_vehicle = {
            grade.vehicle_type: grade.grade for grade in criteria_grades
        }
        assert criteria_grades_by_vehicle[VehicleType.PL] == 2.0
        assert criteria_grades_by_vehicle[VehicleType.VUL] == 2.0
        assert criteria_grades_by_vehicle[VehicleType.VC] == 3.0

    def test_calculate_uses_correct_epoch(self) -> None:
        segments = {10000: create_segment(segment_id=10000)}
        measurements = (
            create_averaged_segment_speed_measurement(
                segment_id=10000, epoch=1, avg_speed=25.0
            ),
            create_averaged_segment_speed_measurement(
                segment_id=10000, epoch=2, avg_speed=35.0
            ),
        )
        speed_data = create_averaged_speed_data(measurements=measurements)
        thresholds = {VehicleType.PL: create_threshold(good=30.0, bad=15.0)}
        territory_data = create_territory_data(segments=segments, speed_data=speed_data)
        strategy = C5SpeedCriteriaCalculationStrategy()

        criteria_grades = strategy.calculate(territory_data, thresholds)

        assert len(criteria_grades) == 2
        criteria_grades_by_epoch = {
            grade.epoch: grade.grade for grade in criteria_grades
        }
        assert criteria_grades_by_epoch[1] == 2.0
        assert criteria_grades_by_epoch[2] == 3.0

    def test_calculate_returns_empty_set_when_speed_data_is_empty(self) -> None:
        segments = {10000: create_segment(segment_id=10000)}
        empty_speed_data = create_averaged_speed_data(measurements=())
        thresholds = {VehicleType.PL: create_threshold(good=30.0, bad=15.0)}
        territory_data = create_territory_data(
            segments=segments, speed_data=empty_speed_data
        )
        strategy = C5SpeedCriteriaCalculationStrategy()

        criteria_grades = strategy.calculate(territory_data, thresholds)

        assert len(criteria_grades) == 0
        assert isinstance(criteria_grades, set)

    def test_calculate_returns_empty_set_when_thresholds_is_empty(self) -> None:
        segments = {10000: create_segment(segment_id=10000)}
        measurements = (
            create_averaged_segment_speed_measurement(segment_id=10000, avg_speed=25.0),
        )
        speed_data = create_averaged_speed_data(measurements=measurements)
        empty_thresholds: Dict[VehicleType, Threshold] = {}
        territory_data = create_territory_data(segments=segments, speed_data=speed_data)
        strategy = C5SpeedCriteriaCalculationStrategy()

        criteria_grades = strategy.calculate(territory_data, empty_thresholds)

        assert len(criteria_grades) == 0
        assert isinstance(criteria_grades, set)


class TestCriteriaCalculatorFactory:
    @pytest.mark.parametrize(
        "criteria_type, expected_strategy",
        [
            (CriteriaType.C5, C5SpeedCriteriaCalculationStrategy),
            (CriteriaType.C11, C11SlopeCriteriaCalculationStrategy),
        ],
    )
    def test_get_calculator_returns_correct_strategy(
        self,
        criteria_type: CriteriaType,
        expected_strategy: type[CriteriaCalculationStrategy],
    ) -> None:
        factory = CriteriaCalculatorFactory()

        calculator = factory.get_calculator(criteria_type)

        assert isinstance(calculator, expected_strategy)

    def test_get_calculator_raises_key_error_for_unknown_criteria(self) -> None:
        factory = CriteriaCalculatorFactory()
        factory._strategies = {}

        with pytest.raises(KeyError):
            factory.get_calculator(CriteriaType.C5)


class TestC11SlopeCriteriaCalculationStrategy:
    def test_calculate_should_assign_proper_grades(self) -> None:
        segments = {10000: create_segment(segment_id=10000)}
        slope_data = frozenset(
            [
                create_slope_segment_measurement(
                    segment_id=10000, slope_percentage=6.0
                ),
            ]
        )
        thresholds = {
            VehicleType.PL: create_threshold(good=5.0, bad=8.0),
            VehicleType.VUL: create_threshold(good=7.0, bad=10.0),
            VehicleType.VC: create_threshold(good=4.0, bad=7.0),
        }
        territory_data = create_territory_data(segments=segments, slope_data=slope_data)
        strategy = C11SlopeCriteriaCalculationStrategy()

        criteria_grades = strategy.calculate(territory_data, thresholds)

        assert len(criteria_grades) == 3
        criteria_grades_by_vehicle = {
            grade.vehicle_type: grade.grade for grade in criteria_grades
        }
        assert criteria_grades_by_vehicle[VehicleType.PL] == 2.0
        assert criteria_grades_by_vehicle[VehicleType.VUL] == 3.0
        assert criteria_grades_by_vehicle[VehicleType.VC] == 2.0

    def test_calculate_returns_empty_set_when_slope_data_is_empty(
        self,
    ) -> None:
        segments = {10000: create_segment(segment_id=10000)}
        empty_slope_data: FrozenSet[SlopeSegmentMeasurement] = frozenset()
        thresholds = {VehicleType.PL: create_threshold(good=5.0, bad=8.0)}
        territory_data = create_territory_data(
            segments=segments, slope_data=empty_slope_data
        )
        strategy = C11SlopeCriteriaCalculationStrategy()

        criteria_grades = strategy.calculate(territory_data, thresholds)

        assert len(criteria_grades) == 0
        assert isinstance(criteria_grades, set)

    def test_calculate_returns_empty_set_when_thresholds_is_empty(
        self,
    ) -> None:
        segments = {10000: create_segment(segment_id=10000)}
        slope_data = frozenset(
            [
                create_slope_segment_measurement(
                    segment_id=10000, slope_percentage=6.0
                ),
            ]
        )
        empty_thresholds: Dict[VehicleType, Threshold] = {}
        territory_data = create_territory_data(segments=segments, slope_data=slope_data)
        strategy = C11SlopeCriteriaCalculationStrategy()

        criteria_grades = strategy.calculate(territory_data, empty_thresholds)

        assert len(criteria_grades) == 0
        assert isinstance(criteria_grades, set)
