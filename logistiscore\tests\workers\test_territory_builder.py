import re
from pathlib import Path
from typing import Any, FrozenSet
from unittest.mock import Mock, patch

import pytest
from shapely.geometry import LineString, Polygon

from logistiscore.ir.slope import SlopeSegmentMeasurement
from logistiscore.ir.territory import Territory, TerritoryData
from logistiscore.tests.factories import (
    create_segment,
    create_slope_segment_measurement,
    create_territory,
)
from logistiscore.workers.territory_builder import TerritoryBuilder


class TestTerritoryBuilder:
    def test_territory_builder_initialization_should_return_empty_list_before_build(
        self, tmp_path: Path
    ) -> None:
        territories_dir = tmp_path / "territories"
        territories_dir.mkdir()

        builder = TerritoryBuilder(str(territories_dir), "")

        assert builder.territories_dir == str(territories_dir)
        assert builder.territories == []

    def test_get_all_territory_names_should_return_only_valid_directories(
        self, territory_structure: Path
    ) -> None:
        builder = TerritoryBuilder(str(territory_structure), "")

        result = builder.get_all_territory_names()

        assert sorted(result) == ["territory1", "territory2"]

    def test_parse_territory_shape_should_return_territory_with_polygon_when_valid(
        self, valid_territory: Path
    ) -> None:
        builder = TerritoryBuilder(str(valid_territory), "")

        result = builder.parse_territory_shape("territory1")

        assert isinstance(result, Territory)
        assert result.name == "territory1"
        assert isinstance(result.geometry, Polygon)

    def test_parse_territory_shape_should_raise_error_when_territory_not_found(
        self, territory_structure: Path
    ) -> None:
        builder = TerritoryBuilder(str(territory_structure), "")

        with pytest.raises(FileNotFoundError):
            builder.parse_territory_shape("nonexistent_territory")

    @patch("geopandas.read_file")
    def test_parse_territory_shape_should_raise_error_when_dataframe_is_empty(
        self, mock_read_file: Any, territory_structure: Path
    ) -> None:
        mock_gdf = Mock()
        mock_projected = Mock()
        mock_projected.empty = True
        mock_gdf.to_crs.return_value = mock_projected
        mock_read_file.return_value = mock_gdf
        builder = TerritoryBuilder(str(territory_structure), "")

        with pytest.raises(ValueError, match="No valid geometries found"):
            builder.parse_territory_shape("territory1")

    def test_parse_territory_shape_should_raise_error_when_multiple_geometries_exist(
        self, multiple_geometries_territory: Path
    ) -> None:
        builder = TerritoryBuilder(str(multiple_geometries_territory), "")
        file_path = str(
            multiple_geometries_territory / "territory1" / "perimeter.geojson"
        )

        with pytest.raises(
            ValueError,
            match=f"Multiple geometries found in {re.escape(file_path)}. Expected one.",
        ):
            builder.parse_territory_shape("territory1")

    def test_parse_territory_shape_should_raise_error_when_geometry_is_empty(
        self, empty_polygon_territory: Path
    ) -> None:
        builder = TerritoryBuilder(str(empty_polygon_territory), "")
        file_path = str(empty_polygon_territory / "territory1" / "perimeter.geojson")

        with pytest.raises(
            ValueError, match=f"Geometry in {re.escape(file_path)} is empty."
        ):
            builder.parse_territory_shape("territory1")

    def test_parse_territory_shape_should_raise_error_when_geometry_is_not_polygon(
        self, point_geometry_territory: Path
    ) -> None:
        builder = TerritoryBuilder(str(point_geometry_territory), "")
        file_path = str(point_geometry_territory / "territory1" / "perimeter.geojson")

        with pytest.raises(
            ValueError,
            match=f"Geometry in {re.escape(file_path)} is not a Polygon",
        ):
            builder.parse_territory_shape("territory1")

    def test_build_should_create_territories_from_valid_directories(
        self, two_valid_territories: Path
    ) -> None:
        builder = TerritoryBuilder(str(two_valid_territories), "")

        builder.build()

        assert len(builder.territories) == 2
        assert all(isinstance(t, Territory) for t in builder.territories)
        territory_names = [t.name for t in builder.territories]
        assert sorted(territory_names) == ["territory1", "territory2"]

    def test_get_territories_should_return_internal_territories_list(
        self, tmp_path: Path
    ) -> None:
        builder = TerritoryBuilder(str(tmp_path), "")
        mock_territory = Mock(spec=Territory)
        builder.territories = [mock_territory]

        result = builder.get_territories()

        assert result == [mock_territory]

    def test_get_territory_directory_returns_correct_path(self, tmp_path: Path) -> None:
        territories_dir = str(tmp_path)
        territory_name = "territory1"
        territory_dir = tmp_path / territory_name
        territory_dir.mkdir()
        builder = TerritoryBuilder(territories_dir, "")

        result = builder._get_territory_directory(territory_name)

        assert result == str(territory_dir)

    def test_get_territory_directory_raises_error_when_directory_not_found(
        self, territory_with_data_files: Path
    ) -> None:
        territories_dir = str(territory_with_data_files)
        territory_name = "nonexistent_territory"
        builder = TerritoryBuilder(territories_dir, "")

        with pytest.raises(FileNotFoundError, match="Territory directory not found"):
            builder._get_territory_directory(territory_name)

    def test_load_territory_segments_loads_real_segments_from_file(
        self, territory_with_data_files: Path
    ) -> None:
        territory_dir = territory_with_data_files / "test_territory"
        builder = TerritoryBuilder(
            territories_dir=str(territory_with_data_files),
            here_api_key="",
            segments_filename="segments.geojson",
        )

        result = builder._load_territory_segments(str(territory_dir))

        assert set(result.keys()) == {10000, 20000}
        assert result[10000].segment_id == 10000
        assert result[10000].name == "coco 1"
        assert result[10000].length == pytest.approx(225244.012)
        assert result[20000].segment_id == 20000
        assert result[20000].name == "coco 2"
        assert isinstance(result[20000].geometry, LineString)

    def test_load_territory_segments_raises_error_when_file_not_found(
        self, territory_with_data_files: Path
    ) -> None:
        territory_dir = str(territory_with_data_files / "test_territory")
        segments_filename = "nonexistent_segments.geojson"
        builder = TerritoryBuilder(
            territories_dir=str(territory_with_data_files),
            here_api_key="",
            segments_filename=segments_filename,
        )

        with pytest.raises(FileNotFoundError, match="Segments file not found"):
            builder._load_territory_segments(territory_dir)

    @patch.object(TerritoryBuilder, "_load_territory_segments")
    def test_load_territory_speed_data_loads_real_data_from_file(
        self, mock_load_segments: Any, territory_with_data_files: Path
    ) -> None:
        territory_dir = str(territory_with_data_files / "test_territory")
        speed_data_filename = "speed_data.csv"
        builder = TerritoryBuilder(
            territories_dir=str(territory_with_data_files),
            here_api_key="",
            speed_data_filename=speed_data_filename,
            segments_filename="segments.geojson",
        )
        mock_segments = {
            10000: create_segment(segment_id=10000),
            20000: create_segment(segment_id=20000),
        }
        mock_load_segments.return_value = mock_segments

        result = builder._load_territory_speed_data(territory_dir, mock_segments)

        assert len(result.measurements) == 4
        segment_ids = {m.segment_id for m in result.measurements}
        assert segment_ids == {10000, 20000}
        epochs = {m.epoch for m in result.measurements}
        assert epochs == {6, 7, 8, 9}

    def test_load_territory_speed_data_raises_error_when_file_not_found(
        self, territory_with_data_files: Path
    ) -> None:
        territory_dir = str(territory_with_data_files / "test_territory")
        speed_data_filename = "nonexistent_speed.csv"
        segments = {10000: create_segment(segment_id=10000)}
        builder = TerritoryBuilder(
            territories_dir=str(territory_with_data_files),
            here_api_key="",
            segments_filename="test_segments.geojson",
            speed_data_filename=speed_data_filename,
        )

        with pytest.raises(FileNotFoundError, match="Speed data file not found"):
            builder._load_territory_speed_data(territory_dir, segments)

    @patch.object(TerritoryBuilder, "_load_territory_segments")
    @patch.object(TerritoryBuilder, "_load_territory_speed_data")
    def test_build_territory_data_integrates_all_components(
        self,
        mock_load_speed_data: Any,
        mock_load_segments: Any,
        territory_with_data_files: Path,
    ) -> None:
        territory = create_territory(name="test_territory")
        builder = TerritoryBuilder(str(territory_with_data_files), "")

        result = builder.build_territory_data(territory)

        assert isinstance(result, TerritoryData)
        assert result.territory == territory
        mock_load_segments.assert_called_once_with(
            str(territory_with_data_files / "test_territory")
        )
        mock_load_speed_data.assert_called_once_with(
            str(territory_with_data_files / "test_territory"),
            mock_load_segments.return_value,
        )

    @patch("logistiscore.workers.territory_builder.logging")
    @patch.object(TerritoryBuilder, "build_territory_data")
    @patch.object(TerritoryBuilder, "parse_territory_shape")
    @patch.object(TerritoryBuilder, "get_all_territory_names")
    def test_build_all_territory_data_should_yield_territory_data_for_each_territory(
        self,
        mock_get_names: Mock,
        mock_parse_shape: Mock,
        mock_build_data: Mock,
        mock_logging: Mock,
        territory_structure: Path,
    ) -> None:
        builder = TerritoryBuilder(str(territory_structure), "")
        territory1 = create_territory(name="territory1")
        territory2 = create_territory(name="territory2")
        territory_data1 = Mock(spec=TerritoryData)
        territory_data2 = Mock(spec=TerritoryData)
        mock_get_names.return_value = ["territory1", "territory2"]
        mock_parse_shape.side_effect = [territory1, territory2]
        mock_build_data.side_effect = [territory_data1, territory_data2]

        result = list(builder.build_all_territory_data())

        mock_logging.info.assert_any_call("Found 2 territories")
        mock_logging.info.assert_any_call("Processing territory territory1")
        mock_logging.info.assert_any_call("Processing territory territory2")
        mock_parse_shape.assert_any_call("territory1")
        mock_parse_shape.assert_any_call("territory2")
        mock_build_data.assert_any_call(territory1)
        mock_build_data.assert_any_call(territory2)
        assert result == [territory_data1, territory_data2]

    @patch("logistiscore.workers.territory_builder.pyogrio")
    @patch("logistiscore.workers.territory_builder.geopandas.read_file")
    def test_parse_territory_shape_should_convert_pyogrio_error_to_file_not_found_error(
        self, mock_gpd_read_file: Mock, mock_pyogrio: Mock, territory_structure: Path
    ) -> None:

        file_path = str(territory_structure / "territory1" / "perimeter.geojson")

        class MockDataSourceError(Exception): ...

        mock_pyogrio.errors.DataSourceError = MockDataSourceError
        mock_gpd_read_file.side_effect = MockDataSourceError("error reading file")
        builder = TerritoryBuilder(str(territory_structure), "")

        expected_message = f"Could not read {file_path}\nerror reading file"
        with pytest.raises(FileNotFoundError, match=re.escape(expected_message)):
            builder.parse_territory_shape("territory1")

    @patch("logistiscore.workers.territory_builder.load_slope_data_from_cache")
    def test_load_territory_slope_data_should_return_cached_data_when_available(
        self, mock_load_slope_data: Any, territory_with_data_files: Path
    ) -> None:
        territory = create_territory(name="test_territory")
        segments = [10000, 20000]
        slope_data = frozenset(
            {
                create_slope_segment_measurement(
                    segment_id=10000, slope_percentage=0.05
                ),
                create_slope_segment_measurement(
                    segment_id=20000, slope_percentage=0.02
                ),
            }
        )
        mock_load_slope_data.return_value = slope_data
        builder = TerritoryBuilder(str(territory_with_data_files), "test_api_key")

        result = builder._load_territory_slope_data(territory, segments)

        assert result == slope_data
        mock_load_slope_data.assert_called_once_with(
            territory_dir=str(territory_with_data_files / "test_territory"),
            slope_data_filename="slope_data.csv",
        )

    @patch("logistiscore.workers.territory_builder.load_slope_data_from_cache")
    @patch("logistiscore.workers.territory_builder.compute_slope_data")
    @patch("logistiscore.workers.territory_builder.save_slope_data_to_cache")
    def test_load_territory_slope_data_should_compute_data_when_cache_file_not_found(
        self,
        mock_save_slope_data: Any,
        mock_compute_slope_data: Any,
        mock_load_slope_data: Any,
        territory_with_data_files: Path,
    ) -> None:
        mock_load_slope_data.side_effect = FileNotFoundError("File not found")
        territory = create_territory(name="test_territory")
        segments = [10000, 20000]
        mock_load_slope_data.side_effect = FileNotFoundError("File not found")
        computed_data = frozenset(
            {
                create_slope_segment_measurement(
                    segment_id=10000, slope_percentage=0.05
                ),
                create_slope_segment_measurement(
                    segment_id=20000, slope_percentage=0.02
                ),
            }
        )
        mock_compute_slope_data.return_value = computed_data
        builder = TerritoryBuilder(str(territory_with_data_files), "test_api_key")

        result = builder._load_territory_slope_data(territory, segments)

        assert result == computed_data
        mock_load_slope_data.assert_called_once()
        mock_compute_slope_data.assert_called_once()

    @patch("logistiscore.workers.territory_builder.load_slope_data_from_cache")
    @patch("logistiscore.workers.territory_builder.compute_slope_data")
    @patch("logistiscore.workers.territory_builder.save_slope_data_to_cache")
    def test_load_territory_slope_data_should_compute_data_when_cache_raises_value_error(
        self,
        mock_save_slope_data: Any,
        mock_compute_slope_data: Any,
        mock_load_slope_data: Any,
        territory_with_data_files: Path,
    ) -> None:
        territory = create_territory(name="test_territory")
        segments = [10000, 20000]
        mock_load_slope_data.side_effect = ValueError("Invalid data format")
        computed_data = frozenset(
            {
                create_slope_segment_measurement(
                    segment_id=10000, slope_percentage=0.05
                ),
                create_slope_segment_measurement(
                    segment_id=20000, slope_percentage=0.02
                ),
            }
        )
        mock_compute_slope_data.return_value = computed_data
        builder = TerritoryBuilder(str(territory_with_data_files), "test_api_key")

        result = builder._load_territory_slope_data(territory, segments)

        assert result == computed_data
        mock_load_slope_data.assert_called_once()
        mock_compute_slope_data.assert_called_once()
        mock_save_slope_data.assert_called_once()

    @patch("logistiscore.workers.territory_builder.load_slope_data_from_cache")
    @patch("logistiscore.workers.territory_builder.compute_slope_data")
    @patch("logistiscore.workers.territory_builder.save_slope_data_to_cache")
    def test_load_territory_slope_data_should_not_save_data_when_computed_data_is_empty(
        self,
        mock_save_slope_data: Any,
        mock_compute_slope_data: Any,
        mock_load_slope_data: Any,
        territory_with_data_files: Path,
    ) -> None:
        territory = create_territory(name="test_territory")
        segments = [10000, 20000]
        mock_load_slope_data.side_effect = FileNotFoundError("File not found")
        computed_data: FrozenSet[SlopeSegmentMeasurement] = frozenset()
        mock_compute_slope_data.return_value = computed_data
        builder = TerritoryBuilder(str(territory_with_data_files), "test_api_key")

        result = builder._load_territory_slope_data(territory, segments)

        assert result == computed_data
        mock_load_slope_data.assert_called_once()
        mock_compute_slope_data.assert_called_once()
        mock_save_slope_data.assert_not_called()
