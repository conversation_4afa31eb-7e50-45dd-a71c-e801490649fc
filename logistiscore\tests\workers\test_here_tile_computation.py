from typing import Any
from unittest.mock import Mock, patch

import pytest

from logistiscore.ir.territory import GeographicBounds, Territory
from logistiscore.ir.tile_layer_mapping import Tile<PERSON>ayerMapping
from logistiscore.workers.here_tile_computation import HERETileComputationWorker


class TestHERETileComputationWorker:
    @pytest.mark.parametrize(
        "level,expected_size",
        [
            (0, 180.0),
            (1, 90.0),
            (2, 45.0),
            (8, 180 / 256),
            (10, 180 / 1024),
        ],
    )
    def test_calculate_tile_size_should_return_expected_size(
        self, level: int, expected_size: float
    ) -> None:
        worker = HERETileComputationWorker()

        result = worker._calculate_tile_size(level)

        assert result == pytest.approx(expected_size, abs=1e-10)

    @pytest.mark.parametrize(
        "tile_x,tile_y,level,expected",
        [
            (0, 0, 0, 0),
            (1, 0, 1, 1),
            (0, 1, 1, 4),
            (1, 1, 1, 5),
            (2, 1, 2, 10),
        ],
    )
    def test_calculate_tile_id_with_should_return_expected_id(
        self, tile_x: int, tile_y: int, level: int, expected: int
    ) -> None:
        worker = HERETileComputationWorker()

        result = worker._calculate_tile_id(tile_x, tile_y, level)

        assert result == expected

    def test_compute_tiles_for_bounds_creates_mappings_for_all_func_classes(
        self,
    ) -> None:
        worker = HERETileComputationWorker()
        bounds = GeographicBounds(
            min_latitude=45.0, max_latitude=45.1, min_longitude=2.0, max_longitude=2.1
        )

        result = worker.compute_tiles_for_bounds(bounds)

        layers_in_result = {mapping.layer for mapping in result}
        expected_layers = {f"ADAS_ATTRIB_FC_{fc}" for fc in worker.FUNC_CLASS_LEVELS}
        assert expected_layers == layers_in_result

    def test_compute_tiles_for_bounds_with_zero_area_bounds(self) -> None:
        worker = HERETileComputationWorker()
        zero_bounds = GeographicBounds(
            min_latitude=45.0, max_latitude=45.0, min_longitude=2.0, max_longitude=2.0
        )

        result = worker.compute_tiles_for_bounds(zero_bounds)

        assert isinstance(result, set)
        assert all(isinstance(mapping, TileLayerMapping) for mapping in result)
        assert len(result) > 0

    @patch.object(HERETileComputationWorker, "compute_tiles_for_bounds")
    def test_compute_tiles_for_territory_returns_set_of_mappings(
        self, mock_compute_tiles_for_bounds: Any
    ) -> None:
        worker = HERETileComputationWorker()
        bounds = GeographicBounds(
            min_latitude=45.0, max_latitude=46.0, min_longitude=2.0, max_longitude=3.0
        )
        territory = Mock(spec=Territory)
        territory.bounds = bounds
        expected_mappings = {
            TileLayerMapping(tile_id=1, layer="ADAS_ATTRIB_FC_1"),
            TileLayerMapping(tile_id=2, layer="ADAS_ATTRIB_FC_2"),
        }
        mock_compute_tiles_for_bounds.return_value = expected_mappings

        result = worker.compute_tiles_for_territory(territory)

        mock_compute_tiles_for_bounds.assert_called_once_with(bounds)
        assert result == expected_mappings
