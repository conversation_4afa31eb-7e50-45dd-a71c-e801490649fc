from datetime import datetime
from typing import Dict, List, Optional, Protocol

from logistiscore.ir.epoch import EpochInterval
from logistiscore.ir.network import Segment
from logistiscore.ir.speed import AveragedSpeedData


class StreetNetworkRepository(Protocol):
    def load_network(
        self, file_path: str
    ) -> Dict[int, Segment]: ...  # pragma: no cover


class FCDRepository(Protocol):
    def get_averaged_speed_data(
        self,
        segment_ids: Optional[List[int]] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        epoch_interval: Optional[EpochInterval] = None,
    ) -> AveragedSpeedData: ...  # pragma: no cover
