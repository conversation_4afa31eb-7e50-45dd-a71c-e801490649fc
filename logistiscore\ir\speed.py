from dataclasses import dataclass
from typing import <PERSON><PERSON>

from logistiscore.ir.epoch import EpochType


@dataclass(frozen=True)
class AveragedSegmentSpeedMeasurement:
    segment_id: int
    epoch: int
    avg_speed: float
    total_count: int
    freeflow: float
    direction: str


@dataclass(frozen=True)
class AveragedSpeedData:
    measurements: Tuple[AveragedSegmentSpeedMeasurement, ...]
    epoch_type: EpochType

    def to_dict(self) -> dict:
        return {
            "segment_id": [m.segment_id for m in self.measurements],
            "epoch": [m.epoch for m in self.measurements],
            "avg_speed": [m.avg_speed for m in self.measurements],
            "total_count": [m.total_count for m in self.measurements],
            "freeflow": [m.freeflow for m in self.measurements],
        }
