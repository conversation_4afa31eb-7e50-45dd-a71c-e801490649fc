from typing import Any
from unittest.mock import Mock, patch

import geopandas as gpd
import pandas as pd
import pytest
from shapely.geometry import LineString

from logistiscore.repositories.here_street_network_repository import (
    HEREStreetNetworkRepository,
    expand_street_directions,
)
from logistiscore.tests.factories import create_segment


class TestHEREStreetNetworkRepository:

    @patch.object(HEREStreetNetworkRepository, "_read_navstreet_data")
    @patch.object(HEREStreetNetworkRepository, "_preprocess")
    @patch.object(HEREStreetNetworkRepository, "_create_segments")
    def test_load_network_calls_preprocess_and_create_segments(
        self,
        mock_create_segments: Any,
        mock_preprocess: Any,
        mock_read_file: Any,
    ) -> None:
        repository = HEREStreetNetworkRepository()
        mock_gdf = Mock(spec=gpd.GeoDataFrame)
        mock_read_file.return_value = mock_gdf

        mock_preprocessed_gdf = Mock(spec=gpd.GeoDataFrame)
        mock_preprocess.return_value = mock_preprocessed_gdf

        expected_segments = {10000: create_segment(segment_id=10000)}
        mock_create_segments.return_value = expected_segments

        result = repository.load_network("path/to/file.geojson")

        mock_read_file.assert_called_once_with("path/to/file.geojson")
        mock_preprocess.assert_called_once_with(mock_gdf, False)
        mock_create_segments.assert_called_once_with(mock_preprocessed_gdf)
        assert result == expected_segments

    @patch(
        "logistiscore.repositories.here_street_network_repository.expand_street_directions"
    )
    @patch.object(HEREStreetNetworkRepository, "_transform_to_internal_schema")
    def test_preprocess_calls_expand_street_directions_and_transform_to_internal_schema(
        self,
        mock_transform_to_internal_schema: Any,
        mock_expand_street_directions: Any,
    ) -> None:
        repository = HEREStreetNetworkRepository()
        gdf = gpd.GeoDataFrame(
            {
                "LINK_ID": ["1", "2"],
                "ST_NAME": ["coco 1", "coco 2"],
                "geometry": [
                    LineString([(0, 0), (1, 1)]),
                    LineString([(0, 0), (2, 2)]),
                ],
            }
        )
        mock_expand_street_directions.return_value = gdf
        mock_transform_to_internal_schema.return_value = gdf

        result = repository._preprocess(gdf, expand_directions=True)

        mock_expand_street_directions.assert_called_once_with(gdf)
        mock_transform_to_internal_schema.assert_called_once_with(gdf, True)
        assert result.equals(gdf)

    @patch(
        "logistiscore.repositories.here_street_network_repository.expand_street_directions"
    )
    def test_preprocess_without_expanding_directions_should_not_duplicate_geoms(
        self, mock_expand_street_directions: Any
    ):
        repository = HEREStreetNetworkRepository()
        gdf = gpd.GeoDataFrame(
            {
                "LINK_ID": ["1", "2"],
                "ST_NAME": ["coco 1", "coco 2"],
                "DIR_TRAVEL": ["F", "T"],
                "geometry": [
                    LineString([(0, 0), (1, 1)]),
                    LineString([(0, 0), (2, 2)]),
                ],
            },
            crs="EPSG:4326",
        )

        _ = repository._preprocess(gdf, expand_directions=False)

        mock_expand_street_directions.assert_not_called()

    def test_validate_columns_with_missing_columns_raises_error(self):
        repository = HEREStreetNetworkRepository()
        gdf = gpd.GeoDataFrame(
            {
                "LINK_ID": ["1", "2"],
                "geometry": [
                    LineString([(0, 0), (1, 1)]),
                    LineString([(0, 0), (2, 2)]),
                ],
            }
        )

        with pytest.raises(ValueError, match="Missing required columns: ST_NAME"):
            repository._validate_columns(gdf, ["LINK_ID", "ST_NAME"])

    def test_validate_columns_with_all_columns_passes(self):
        repository = HEREStreetNetworkRepository()
        gdf = gpd.GeoDataFrame(
            {
                "LINK_ID": ["1", "2"],
                "ST_NAME": ["coco 1", "coco 2"],
                "geometry": [
                    LineString([(0, 0), (1, 1)]),
                    LineString([(0, 0), (2, 2)]),
                ],
            }
        )

        repository._validate_columns(gdf, ["LINK_ID", "ST_NAME"])

    @patch.object(HEREStreetNetworkRepository, "_compute_segments_length")
    def test_transform_to_internal_schema_creates_minimal_gdf(
        self, mock_compute_length: Any
    ) -> None:
        repository = HEREStreetNetworkRepository()
        gdf = gpd.GeoDataFrame(
            {
                "LINK_ID": [10000, 20000],
                "ST_NAME": ["coco 1", "coco 2"],
                "geometry": [
                    LineString([(0, 0), (1, 1)]),
                    LineString([(0, 0), (2, 2)]),
                ],
            }
        )
        mock_compute_length.return_value = pd.Series([1.414, 2.828])

        result = repository._transform_to_internal_schema(gdf, expand_directions=False)

        expected_gdf = gpd.GeoDataFrame(
            {
                "segment_id": [10000, 20000],
                "name": ["coco 1", "coco 2"],
                "geometry": [
                    LineString([(0, 0), (1, 1)]),
                    LineString([(0, 0), (2, 2)]),
                ],
                "length": [1.414, 2.828],
            }
        )
        assert result.equals(expected_gdf)

    def test_create_segments_converts_dataframe_to_segment_objects(self):
        repository = HEREStreetNetworkRepository()
        gdf = gpd.GeoDataFrame(
            {
                "segment_id": [10000, 20000],
                "name": ["coco 1", "coco 2"],
                "geometry": [
                    LineString([(0, 0), (1, 1)]),
                    LineString([(0, 0), (2, 2)]),
                ],
                "length": [1.414, 2.828],
            }
        )

        result = repository._create_segments(gdf)

        expected_segments = {
            10000: create_segment(
                segment_id=10000,
                name="coco 1",
                geometry=LineString([(0, 0), (1, 1)]),
                length=1.414,
                direction=None,
            ),
            20000: create_segment(
                segment_id=20000,
                name="coco 2",
                geometry=LineString([(0, 0), (2, 2)]),
                length=2.828,
                direction=None,
            ),
        }
        assert list(result.keys()) == list(expected_segments.keys())
        assert list(result.values()) == list(expected_segments.values())

    def test_create_segment_from_row_includes_direction_if_present(self):
        repository = HEREStreetNetworkRepository()
        row = pd.Series(
            {
                "segment_id": 10000,
                "name": "coco 1",
                "geometry": LineString([(0, 0), (1, 1)]),
                "length": 1.414,
                "direction": "F",
            }
        )

        result = repository._create_segment_from_row(row, has_direction=True)

        expected_segment = create_segment(
            segment_id=10000,
            name="coco 1",
            geometry=LineString([(0, 0), (1, 1)]),
            length=1.414,
            direction="F",
        )
        assert result == expected_segment

    @patch(
        "logistiscore.repositories.here_street_network_repository.gpd.GeoSeries.to_crs"
    )
    def test_compute_segments_length_converts_to_swiss_projection(
        self, mock_to_crs: Any
    ):
        repository = HEREStreetNetworkRepository()
        geometries = gpd.GeoSeries(
            [
                LineString([(8.5, 47.3), (8.6, 47.4)]),
                LineString([(8.2, 46.8), (8.4, 47.0)]),
            ]
        )
        mock_projected = Mock(spec=gpd.GeoSeries)
        mock_projected.length = pd.Series([1000.123, 2500.456])
        mock_to_crs.return_value = mock_projected

        result = repository._compute_segments_length(geometries)
        mock_to_crs.assert_called_once_with("EPSG:21781")
        assert result.equals(pd.Series([1000.123, 2500.456]).round(3))

    def test_compute_segments_length_ensures_minimum_length(self):
        repository = HEREStreetNetworkRepository()
        geometries = gpd.GeoSeries([LineString([(0, 0), (0.0000001, 0.0000001)])])

        with patch.object(gpd.GeoSeries, "to_crs") as mock_to_crs:
            mock_projected = Mock(spec=gpd.GeoSeries)
            mock_projected.length = pd.Series([0.5])
            mock_to_crs.return_value = mock_projected

            result = repository._compute_segments_length(geometries)
            assert result.equals(pd.Series([1.0]))

    @patch.object(HEREStreetNetworkRepository, "_read_navstreet_data")
    @patch(
        "logistiscore.repositories.here_street_network_repository.expand_street_directions"
    )
    @patch.object(HEREStreetNetworkRepository, "_compute_segments_length")
    @patch.object(HEREStreetNetworkRepository, "_create_segments")
    def test_load_network_calls_all_methods(
        self,
        mock_create_segments: Any,
        mock_compute_length: Any,
        mock_expand_street_directions: Any,
        mock_read_file: Any,
    ) -> None:
        repository = HEREStreetNetworkRepository()
        mock_gdf = gpd.GeoDataFrame(
            {
                "LINK_ID": [10000, 20000],
                "ST_NAME": ["coco 1", "coco 2"],
                "direction": ["F", "T"],
                "geometry": [
                    LineString([(0, 0), (1, 1)]),
                    LineString([(0, 0), (2, 2)]),
                ],
            }
        )
        mock_read_file.return_value = mock_gdf
        mock_expand_street_directions.return_value = mock_gdf
        mock_compute_length.return_value = pd.Series([1.414, 2.828])
        expected_segments = {
            10000: create_segment(segment_id=10000),
            20000: create_segment(segment_id=20000),
        }
        mock_create_segments.return_value = expected_segments

        result = repository.load_network("path/to/file.geojson", expand_directions=True)

        mock_read_file.assert_called_once_with("path/to/file.geojson")
        mock_expand_street_directions.assert_called_once_with(mock_gdf)
        mock_compute_length.assert_called_once()
        mock_create_segments.assert_called_once()
        assert result == expected_segments

    @patch.object(HEREStreetNetworkRepository, "_validate_columns")
    @patch("logistiscore.repositories.here_street_network_repository.gpd.read_file")
    def test_read_navstreet_data_successfully_loads_file(
        self, mock_read_file: Any, mock_validate_columns: Any
    ) -> None:
        repository = HEREStreetNetworkRepository()
        gdf = gpd.GeoDataFrame(
            {
                "LINK_ID": ["1", "2"],
                "ST_NAME": ["coco 1", "coco 2"],
                "DIR_TRAVEL": ["F", "T"],
                "geometry": [
                    LineString([(0, 0), (1, 1)]),
                    LineString([(0, 0), (2, 2)]),
                ],
            },
            crs="EPSG:4326",
        )
        mock_read_file.return_value = gdf

        result = repository._read_navstreet_data("path/to/file.geojson")

        mock_read_file.assert_called_once_with(
            "path/to/file.geojson", columns=repository.required_raw_here_columns
        )
        mock_validate_columns.assert_called_once_with(
            gdf, repository.required_raw_here_columns
        )
        assert result.equals(gdf)

    @patch("logistiscore.repositories.here_street_network_repository.gpd.read_file")
    @patch(
        "logistiscore.repositories.here_street_network_repository."
        "gpd.GeoDataFrame.set_crs"
    )
    @patch.object(HEREStreetNetworkRepository, "_validate_columns")
    def test_read_navstreet_data_sets_crs_when_none(
        self, mock_validate_columns: Any, mock_set_crs: Any, mock_read_file: Any
    ) -> None:
        repository = HEREStreetNetworkRepository()
        gdf = gpd.GeoDataFrame(
            {
                "LINK_ID": ["1", "2"],
                "ST_NAME": ["coco 1", "coco 2"],
                "DIR_TRAVEL": ["F", "T"],
                "geometry": [
                    LineString([(0, 0), (1, 1)]),
                    LineString([(0, 0), (2, 2)]),
                ],
            },
            crs=None,
        )
        mock_read_file.return_value = gdf

        result = repository._read_navstreet_data("path/to/file.geojson")

        mock_set_crs.assert_called_once_with(epsg=4326, inplace=True)
        mock_read_file.assert_called_once_with(
            "path/to/file.geojson", columns=repository.required_raw_here_columns
        )
        mock_validate_columns.assert_called_once_with(
            gdf, repository.required_raw_here_columns
        )
        assert result.equals(gdf)

    @patch("logistiscore.repositories.here_street_network_repository.gpd.read_file")
    def test_read_navstreet_data_raises_error_on_file_load_failure(
        self, mock_read_file: Any
    ) -> None:
        repository = HEREStreetNetworkRepository()
        mock_read_file.side_effect = Exception("File not found")

        with pytest.raises(
            ValueError, match="Error loading HERE NavStreet network: File not found"
        ):
            repository._read_navstreet_data("path/to/nonexistent.geojson")


class TestPreprocessStreets:
    def test_preprocess_street_forward_direction_should_add_f_suffix(self) -> None:
        geometry_data = [LineString([(0, 0), (1, 1)])]
        street_data = {"LINK_ID": ["1"], "DIR_TRAVEL": ["F"], "geometry": geometry_data}
        gdf_street = gpd.GeoDataFrame(street_data, geometry="geometry")

        result = expand_street_directions(gdf_street)

        assert len(result) == 1
        assert result["LINK_DIR"].iloc[0] == "1F"
        assert isinstance(result, gpd.GeoDataFrame)

    def test_preprocess_street_toward_direction_should_add_t_suffix_and_reverse_geometry(
        self,
    ) -> None:
        original_geometry = LineString([(0, 0), (1, 1)])
        street_data = {
            "LINK_ID": ["1"],
            "DIR_TRAVEL": ["T"],
            "geometry": [original_geometry],
        }
        gdf_street = gpd.GeoDataFrame(street_data, geometry="geometry")

        result = expand_street_directions(gdf_street)

        assert len(result) == 1
        assert result["LINK_DIR"].iloc[0] == "1T"
        assert isinstance(result, gpd.GeoDataFrame)
        reversed_geometry = original_geometry.parallel_offset(0.00005, "left")
        assert result.geometry.iloc[0].equals(reversed_geometry)

    def test_preprocess_street_both_directions_should_create_two_records(self) -> None:
        geometry_data = [LineString([(0, 0), (1, 1)])]
        street_data = {"LINK_ID": ["1"], "DIR_TRAVEL": ["B"], "geometry": geometry_data}
        gdf_street = gpd.GeoDataFrame(street_data, geometry="geometry")

        result = expand_street_directions(gdf_street)

        assert len(result) == 2
        assert set(result["LINK_DIR"]) == {"1F", "1T"}
        assert isinstance(result, gpd.GeoDataFrame)

    def test_preprocess_street_mixed_directions_should_process_accordingly(
        self,
    ) -> None:
        geometry_data = [
            LineString([(0, 0), (1, 1)]),
            LineString([(2, 2), (3, 3)]),
            LineString([(4, 4), (5, 5)]),
        ]
        street_data = {
            "LINK_ID": ["1", "2", "3"],
            "DIR_TRAVEL": ["F", "T", "B"],
            "geometry": geometry_data,
        }
        gdf_street = gpd.GeoDataFrame(street_data, geometry="geometry")

        result = expand_street_directions(gdf_street)

        assert len(result) == 4
        assert set(result["LINK_DIR"]) == {"1F", "2T", "3F", "3T"}
        assert isinstance(result, gpd.GeoDataFrame)

    def test_preprocess_street_should_apply_parallel_offset(self) -> None:
        original_geometry = LineString([(0, 0), (1, 1)])
        street_data = {
            "LINK_ID": ["1"],
            "DIR_TRAVEL": ["F"],
            "geometry": [original_geometry],
        }
        gdf_street = gpd.GeoDataFrame(street_data, geometry="geometry")

        result = expand_street_directions(gdf_street)

        assert len(result) == 1
        assert result.geometry.iloc[0] != original_geometry
        expected_offset = original_geometry.parallel_offset(0.00005, "right")
        assert result.geometry.iloc[0].equals(expected_offset)
        assert isinstance(result, gpd.GeoDataFrame)

    @pytest.mark.parametrize(
        ["direction", "expected_count", "expected_suffixes"],
        [("F", 1, ["F"]), ("T", 1, ["T"]), ("B", 2, ["F", "T"])],
    )
    def test_preprocess_street_with_different_directions(
        self, direction: str, expected_count: int, expected_suffixes: list[str]
    ) -> None:
        geometry_data = [LineString([(0, 0), (1, 1)])]
        street_data = {
            "LINK_ID": ["1"],
            "DIR_TRAVEL": [direction],
            "geometry": geometry_data,
        }
        gdf_street = gpd.GeoDataFrame(street_data, geometry="geometry")

        result = expand_street_directions(gdf_street)

        assert len(result) == expected_count
        suffixes = [link_dir[-1] for link_dir in result["LINK_DIR"]]
        assert set(suffixes) == set(expected_suffixes)
        assert isinstance(result, gpd.GeoDataFrame)
